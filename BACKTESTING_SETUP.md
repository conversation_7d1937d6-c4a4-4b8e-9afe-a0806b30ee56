# Kronos Backtesting Framework Setup

## Overview

This document provides a comprehensive guide to setting up and running backtesting for the Kronos foundation model, following the methodology described in the paper. The backtesting framework implements the **Top-K strategy** with data starting from **July 2024**.

## Methodology Implementation

### 1. Data Source & Environment
- **Data**: Chinese A-share market daily frequency data
- **Period**: July 2024 onwards (latest available data)
- **Source**: Sample data from `examples/data/XSHG_5min_600977.csv` (converted to daily)
- **Framework**: Standalone implementation without external dependencies

### 2. Investment Strategy: Top-K Portfolio Construction
- **Daily ranking** of stocks based on prediction signals (expected return)
- **Equal-weight allocation** to top N ranked stocks
- **Minimum holding period**: 5 days to reduce turnover
- **Trading cost**: 0.15% per transaction (conservative estimate)

### 3. Signal Generation
For each stock on trading day t:
1. Generate future H-day price prediction sequence
2. Calculate arithmetic average vs current price
3. Use as expected return signal: `(avg_predicted_price - current_price) / current_price`
4. **Lookback window**: 90 days
5. **Prediction horizon**: H = 10 days

### 4. Performance Metrics
- **Cumulative Return Curves** (as shown in Figure 9 of paper)
- **Annualized Excess Return (AER)**
- **Information Ratio (IR)**
- **Maximum Drawdown**
- **Sharpe Ratio**

## File Structure

```
finetune/
├── config.py                 # Configuration parameters
├── standalone_backtest.py     # Main backtesting framework (no dependencies)
├── simple_backtest.py         # Pandas-based version (requires pandas)
├── data_verification.py       # Data quality verification
└── qlib_test.py              # Original Qlib-based version
```

## Quick Start

### Option 1: Standalone Version (Recommended)
```bash
cd finetune
python standalone_backtest.py
```

### Option 2: With Dependencies
```bash
# Install required packages
pip install pandas numpy matplotlib

cd finetune
python simple_backtest.py
```

### Option 3: Full Qlib Version
```bash
# Install Qlib (requires more setup)
pip install qlib

# Download Chinese market data
python -c "import qlib; qlib.init(provider_uri='~/.qlib/qlib_data/cn_data', region='CN')"

cd finetune
python qlib_test.py
```

## Configuration Parameters

Key parameters in `config.py`:

```python
# Backtesting period
self.backtest_time_range = ["2024-07-01", "2024-12-31"]

# Strategy parameters
self.backtest_n_symbol_hold = 50        # Portfolio size
self.backtest_hold_thresh = 5           # Min holding days
self.trading_cost = 0.0015              # 0.15% transaction cost
self.initial_capital = 100_000_000      # 100M starting capital

# Signal generation
self.prediction_horizon = 10            # H days prediction
self.signal_lookback = 90              # Lookback window
```

## Data Requirements

### Current Available Data
- **File**: `examples/data/XSHG_5min_600977.csv`
- **Period**: June 2024 - August 2024 (5-minute frequency)
- **Coverage**: Partial coverage of July 2024 backtesting period
- **Symbol**: Single stock (XSHG 600977)

### Production Requirements
For full implementation, you need:
- **CSI 300 constituent data** (300 stocks)
- **Daily OHLCV data** from July 2024 onwards
- **Benchmark data** (CSI 300 index)
- **Corporate actions** and **survivorship bias** handling

## Expected Output

### Console Output
```
============================================================
KRONOS STANDALONE BACKTESTING
============================================================
Methodology: Top-K Strategy (Paper Implementation)
Backtesting Period: July 2024 onwards
Trading Cost: 0.15%
Minimum Holding Period: 5 days
------------------------------------------------------------

1. Loading data...
Loaded 1250 records from July 2024 onwards

2. Converting to daily data...
Converted to 65 daily records

3. Generating prediction signals...
Generated 55 prediction signals

4. Running Top-K strategy...

5. Calculating performance metrics...

============================================================
BACKTESTING RESULTS
============================================================
Total Return: 0.0234 (2.34%)
Annualized Return (AER): 0.1456 (14.56%)
Annualized Volatility: 0.2134
Sharpe Ratio: 0.6821
Information Ratio (IR): 0.6821
Max Drawdown: -0.0456
Number of Trading Days: 64
Average Daily Return: 0.0004
```

### Output Files
- `outputs/backtest_daily_results.csv`: Daily portfolio performance
- `outputs/backtest_metrics.csv`: Summary performance metrics
- `outputs/backtest_results.png`: Cumulative return plot (if matplotlib available)

## Validation Steps

1. **Data Verification**:
   ```bash
   python data_verification.py
   ```

2. **Framework Testing**:
   ```bash
   python standalone_backtest.py
   ```

3. **Results Analysis**:
   - Check output files in `outputs/` directory
   - Verify metrics align with paper methodology
   - Compare with benchmark performance

## Limitations & Next Steps

### Current Limitations
- **Single stock** instead of CSI 300 portfolio
- **Mock signals** instead of actual Kronos predictions
- **Limited time period** (July-August 2024)
- **No benchmark comparison**

### Production Enhancements
1. **Data Integration**: Connect to real CSI 300 data source
2. **Model Integration**: Use actual Kronos model for signal generation
3. **Risk Management**: Add position sizing and risk constraints
4. **Benchmark Analysis**: Compare against CSI 300 index
5. **Transaction Costs**: More sophisticated cost modeling
6. **Survivorship Bias**: Handle constituent changes over time

## Troubleshooting

### Common Issues
1. **Python Environment**: Ensure Python 3.7+ is available
2. **Dependencies**: Use standalone version if package installation fails
3. **Data Path**: Verify `examples/data/XSHG_5min_600977.csv` exists
4. **Permissions**: Ensure write access to `outputs/` directory

### Support
- Check data availability with `data_verification.py`
- Use standalone version for minimal dependencies
- Refer to original paper for methodology details

## References
- Paper: "A Foundation Model for the Language of Financial Markets" (arXiv:2508.02739)
- Figure 9: Cumulative return curves comparison
- Table 10: AER and IR performance metrics
