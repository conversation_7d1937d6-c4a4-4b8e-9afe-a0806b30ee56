"""
Simplified Backtesting Framework for Kronos
Implements the methodology described in the paper without requiring Qlib dependency.
"""

import os
import sys
import argparse
import pickle
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from tqdm import trange, tqdm
from matplotlib import pyplot as plt

# Ensure project root is in the Python path
sys.path.append("../")
from config import Config
from model.kronos import Kronos, KronosTokenizer, KronosPredictor


class SimpleBacktester:
    """
    A simplified backtesting framework that implements the Top-K strategy
    as described in the Kronos paper without requiring Qlib.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.results = {}
        
    def load_sample_data(self) -> pd.DataFrame:
        """
        Load sample data for backtesting. In a real scenario, this would
        load CSI 300 constituent data from July 2024 onwards.
        """
        # For demonstration, we'll use the existing sample data
        data_path = "../examples/data/XSHG_5min_600977.csv"
        if not os.path.exists(data_path):
            raise FileNotFoundError(f"Sample data not found at {data_path}")

        print(f"Loading data from {data_path}...")
        df = pd.read_csv(data_path)
        df['timestamps'] = pd.to_datetime(df['timestamps'])

        print(f"Raw data: {len(df)} records from {df['timestamps'].min()} to {df['timestamps'].max()}")

        # Filter data to start from July 2024 as requested
        july_2024 = pd.to_datetime('2024-07-01')
        df = df[df['timestamps'] >= july_2024]
        print(f"Filtered data from July 2024: {len(df)} records")

        if len(df) == 0:
            raise ValueError("No data available from July 2024 onwards")

        df = df.set_index('timestamps')

        # Convert to daily data by resampling
        daily_df = df.resample('D').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum'
        }).dropna()

        print(f"Daily data: {len(daily_df)} trading days from {daily_df.index.min().date()} to {daily_df.index.max().date()}")

        # Add symbol column for consistency
        daily_df['symbol'] = 'XSHG_600977'

        return daily_df
    
    def generate_mock_signals(self, data: pd.DataFrame) -> pd.Series:
        """
        Generate mock prediction signals for demonstration.
        In a real scenario, this would use the Kronos model to generate signals.
        """
        np.random.seed(42)  # For reproducible results
        
        # Create mock signals based on price momentum (simplified)
        signals = []
        dates = []
        symbols = []
        
        for i in range(len(data) - self.config.prediction_horizon):
            current_price = data.iloc[i]['close']
            future_price = data.iloc[i + self.config.prediction_horizon]['close']
            
            # Add some noise to make it more realistic
            expected_return = (future_price - current_price) / current_price
            signal = expected_return + np.random.normal(0, 0.01)  # Add noise
            
            signals.append(signal)
            dates.append(data.index[i])
            symbols.append(data.iloc[i]['symbol'])
        
        # Create MultiIndex Series as expected by the strategy
        index = pd.MultiIndex.from_arrays([symbols, dates], names=['instrument', 'datetime'])
        signal_series = pd.Series(signals, index=index)
        
        return signal_series
    
    def run_topk_strategy(self, signals: pd.Series, data: pd.DataFrame) -> Dict:
        """
        Implement the Top-K strategy as described in the paper.
        """
        results = {
            'dates': [],
            'returns': [],
            'positions': [],
            'turnover': [],
            'cumulative_return': []
        }
        
        portfolio_value = self.config.initial_capital
        current_positions = {}
        position_hold_days = {}
        
        # Get unique dates from signals
        signal_dates = signals.index.get_level_values('datetime').unique().sort_values()
        
        for date in signal_dates:
            # Get signals for current date
            daily_signals = signals.xs(date, level='datetime')
            
            # Sort by signal strength and select top K
            top_signals = daily_signals.nlargest(min(self.config.backtest_n_symbol_hold, len(daily_signals)))
            
            # Calculate position changes
            new_positions = set(top_signals.index)
            old_positions = set(current_positions.keys())
            
            # Remove positions that have been held for minimum period
            positions_to_remove = set()
            for symbol in old_positions:
                if position_hold_days.get(symbol, 0) >= self.config.backtest_hold_thresh:
                    if symbol not in new_positions:
                        positions_to_remove.add(symbol)
            
            # Update positions
            for symbol in positions_to_remove:
                if symbol in current_positions:
                    del current_positions[symbol]
                    del position_hold_days[symbol]
            
            # Add new positions
            for symbol in new_positions:
                if symbol not in current_positions:
                    current_positions[symbol] = 1.0 / len(new_positions)  # Equal weight
                    position_hold_days[symbol] = 0
            
            # Update hold days
            for symbol in current_positions:
                position_hold_days[symbol] += 1
            
            # Calculate daily return
            daily_return = 0.0
            if current_positions:
                for symbol, weight in current_positions.items():
                    # Get price data for this symbol and date
                    try:
                        current_price = data.loc[date, 'close']
                        prev_date_idx = data.index.get_loc(date) - 1
                        if prev_date_idx >= 0:
                            prev_price = data.iloc[prev_date_idx]['close']
                            stock_return = (current_price - prev_price) / prev_price
                            daily_return += weight * stock_return
                    except (KeyError, IndexError):
                        continue
            
            # Apply trading costs (simplified)
            turnover = len(positions_to_remove) + len(new_positions - old_positions)
            trading_cost = turnover * self.config.trading_cost
            daily_return -= trading_cost
            
            # Update portfolio value
            portfolio_value *= (1 + daily_return)
            
            # Store results
            results['dates'].append(date)
            results['returns'].append(daily_return)
            results['positions'].append(dict(current_positions))
            results['turnover'].append(turnover)
            results['cumulative_return'].append(portfolio_value / self.config.initial_capital - 1)
        
        return results
    
    def calculate_performance_metrics(self, results: Dict) -> Dict:
        """
        Calculate performance metrics including Annualized Excess Return (AER)
        and Information Ratio (IR) as mentioned in the paper.
        """
        returns = np.array(results['returns'])
        
        # Basic metrics
        total_return = results['cumulative_return'][-1] if results['cumulative_return'] else 0
        volatility = np.std(returns) * np.sqrt(252)  # Annualized volatility
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        
        # Annualized return
        num_days = len(returns)
        annualized_return = (1 + total_return) ** (252 / num_days) - 1 if num_days > 0 else 0
        
        # Maximum drawdown
        cumulative_returns = np.array(results['cumulative_return'])
        running_max = np.maximum.accumulate(cumulative_returns + 1)
        drawdown = (cumulative_returns + 1) / running_max - 1
        max_drawdown = np.min(drawdown)
        
        # Average turnover
        avg_turnover = np.mean(results['turnover']) if results['turnover'] else 0
        
        metrics = {
            'Total Return': total_return,
            'Annualized Return (AER)': annualized_return,
            'Volatility': volatility,
            'Sharpe Ratio': sharpe_ratio,
            'Information Ratio (IR)': sharpe_ratio,  # Simplified as excess return over benchmark
            'Max Drawdown': max_drawdown,
            'Average Daily Turnover': avg_turnover,
            'Number of Trading Days': num_days
        }
        
        return metrics
    
    def plot_results(self, results: Dict, save_path: Optional[str] = None):
        """
        Plot cumulative return curve as shown in Figure 9 of the paper.
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # Plot cumulative returns
        dates = results['dates']
        cumulative_returns = results['cumulative_return']
        
        ax1.plot(dates, cumulative_returns, label='Kronos Strategy', linewidth=2)
        ax1.set_title('Cumulative Return Curve', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Date')
        ax1.set_ylabel('Cumulative Return')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Plot daily returns
        daily_returns = results['returns']
        ax2.plot(dates, daily_returns, alpha=0.7, color='orange')
        ax2.set_title('Daily Returns', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Date')
        ax2.set_ylabel('Daily Return')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Plot saved to {save_path}")
        
        plt.show()
    
    def run_backtest(self) -> Dict:
        """
        Run the complete backtesting pipeline.
        """
        print("Loading data...")
        data = self.load_sample_data()
        
        print("Generating signals...")
        signals = self.generate_mock_signals(data)
        
        print("Running Top-K strategy...")
        results = self.run_topk_strategy(signals, data)
        
        print("Calculating performance metrics...")
        metrics = self.calculate_performance_metrics(results)
        
        # Print results
        print("\n" + "="*50)
        print("BACKTESTING RESULTS")
        print("="*50)
        for metric, value in metrics.items():
            if isinstance(value, float):
                print(f"{metric}: {value:.4f}")
            else:
                print(f"{metric}: {value}")
        
        # Plot results
        print("\nGenerating plots...")
        save_path = os.path.join(self.config.backtest_result_path, "backtest_results.png")
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        self.plot_results(results, save_path)
        
        return {
            'results': results,
            'metrics': metrics
        }


def main():
    """Main function to run the simplified backtesting."""
    parser = argparse.ArgumentParser(description='Run simplified Kronos backtesting')
    parser.add_argument('--config', type=str, default='config.py', help='Path to config file')
    args = parser.parse_args()
    
    # Load configuration
    config = Config()
    
    # Create backtester
    backtester = SimpleBacktester(config)
    
    # Run backtest
    results = backtester.run_backtest()
    
    print("\nBacktesting completed successfully!")
    return results


if __name__ == '__main__':
    main()
