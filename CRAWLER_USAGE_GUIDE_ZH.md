# 股票資料爬蟲使用指南

## 📊 概述

本指南說明如何使用爬蟲腳本抓取缺失的股票資料期間：**2024年8月30日 ~ 2025年7月31日**

## 🕷️ 可用的爬蟲腳本

### 1. 完整功能爬蟲 (推薦)
**檔案**: `scripts/web_scraper_extended_data.py`

**特點**:
- 支援多個資料源 (新浪財經、東方財富、網易財經)
- 自動獲取CSI300成分股清單
- 資料驗證和清理功能
- 錯誤處理和重試機制

**使用方法**:
```bash
# 安裝依賴
pip install requests pandas numpy

# 執行爬蟲
python scripts/web_scraper_extended_data.py
```

### 2. 簡化爬蟲 (輕量級)
**檔案**: `scripts/simple_data_crawler.py`

**特點**:
- 輕量級實現，依賴最少
- 內建模擬資料生成
- 適合測試和演示
- 更穩定的API調用

**使用方法**:
```bash
# 執行簡化爬蟲
python scripts/simple_data_crawler.py
```

## 📋 爬蟲執行步驟

### 步驟 1: 環境準備
```bash
# 確保Python環境可用
python --version

# 安裝必要套件
pip install requests pandas numpy json csv datetime
```

### 步驟 2: 執行爬蟲
```bash
# 選擇其中一個爬蟲執行
python scripts/simple_data_crawler.py

# 或者執行完整版本
python scripts/web_scraper_extended_data.py
```

### 步驟 3: 驗證結果
```bash
# 檢查爬取的資料
python scripts/validate_extended_data.py

# 查看輸出檔案
ls -la data/crawled/
ls -la data/scraped/
```

### 步驟 4: 執行回測
```bash
# 使用爬取的資料執行回測
cd finetune
python standalone_backtest.py
```

## 📁 輸出檔案結構

```
data/
├── crawled/                    # 簡化爬蟲輸出
│   ├── crawled_stock_data.csv  # 主要資料檔案
│   └── failed_symbols.txt     # 失敗的股票清單
├── scraped/                    # 完整爬蟲輸出
│   ├── scraped_stock_data.csv  # 主要資料檔案
│   └── failed_symbols.txt     # 失敗的股票清單
└── extended/                   # 其他資料來源
    └── ...
```

## 📊 資料格式

爬取的CSV檔案格式：
```csv
date,symbol,open,high,low,close,volume,amount
2024-08-30,000001.SZ,10.50,10.60,10.45,10.55,1000000,10550000
2024-08-30,600000.SH,15.20,15.35,15.10,15.30,800000,12240000
...
```

**欄位說明**:
- `date`: 交易日期 (YYYY-MM-DD)
- `symbol`: 股票代碼 (如 000001.SZ, 600000.SH)
- `open`: 開盤價
- `high`: 最高價
- `low`: 最低價
- `close`: 收盤價
- `volume`: 成交量
- `amount`: 成交額

## ⚙️ 爬蟲配置

### 可調整參數

**時間範圍**:
```python
self.start_date = "2024-08-30"  # 開始日期
self.end_date = "2025-07-31"    # 結束日期
```

**股票清單**:
```python
self.stock_symbols = [
    '000001', '000002', '000858',  # 深圳股票
    '600000', '600036', '600519'   # 上海股票
]
```

**請求設定**:
```python
self.delay = 1          # 請求間隔(秒)
self.timeout = 15       # 請求超時(秒)
self.retry_count = 3    # 重試次數
```

## 🔧 故障排除

### 常見問題

#### 1. 網路連接問題
**症狀**: 請求超時或連接失敗
**解決方案**:
```bash
# 檢查網路連接
ping finance.sina.com.cn
ping push2.eastmoney.com

# 調整超時設定
# 在腳本中增加 timeout 參數
```

#### 2. API限制
**症狀**: 返回空資料或錯誤代碼
**解決方案**:
```python
# 增加請求間隔
self.delay = 2  # 改為2秒間隔

# 添加隨機延遲
import random
time.sleep(random.uniform(1, 3))
```

#### 3. 資料格式錯誤
**症狀**: CSV檔案格式不正確
**解決方案**:
```bash
# 檢查輸出檔案
head -10 data/crawled/crawled_stock_data.csv

# 手動驗證資料
python scripts/validate_extended_data.py
```

### 手動測試方法

如果自動爬蟲失敗，可以手動測試API：

#### 測試新浪財經API
```bash
# 使用curl測試
curl "https://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData?symbol=sh600000&scale=240&ma=no&datalen=10"
```

#### 測試東方財富API
```bash
# 使用curl測試
curl "http://push2his.eastmoney.com/api/qt/stock/kline/get?secid=1.600000&ut=fa5fd1943c7b386f172d6893dbfba10b&fields1=f1,f2,f3,f4,f5,f6&fields2=f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61&klt=101&fqt=1&beg=20240830&end=20241030&lmt=100"
```

## 📈 預期結果

### 成功執行後的輸出
```
======================================================================
🕷️ 簡化股票資料爬蟲
======================================================================
📅 目標期間: 2024-08-30 ~ 2025-07-31
📊 股票數量: 16
📁 輸出目錄: data/crawled
----------------------------------------------------------------------

進度: 1/16
📈 正在爬取 000001.SZ...
✅ 000001.SZ 成功獲取 245 條記錄

...

======================================================================
📊 爬取結果統計
======================================================================
✅ 成功處理: 16/16 隻股票
📈 總記錄數: 3920
📅 資料期間: 2024-08-30 ~ 2025-07-31
📊 平均每隻股票: 245 條記錄

🎉 爬蟲完成！
```

### 資料品質檢查
```bash
# 執行驗證
python scripts/validate_extended_data.py

# 預期輸出
✅ 資料檔案找到: data/crawled/crawled_stock_data.csv
📊 資料形狀: (3920, 8)
📅 日期範圍: 2024-08-30 到 2025-07-31
📈 股票數量: 16
✅ 無缺失值
✅ 價格邏輯正確
```

## 🚀 進階使用

### 自定義股票清單
```python
# 編輯 simple_data_crawler.py
self.stock_symbols = [
    '000001', '000002',  # 添加您需要的股票代碼
    '600000', '600036'
]
```

### 批量處理
```bash
# 分批執行避免API限制
python scripts/simple_data_crawler.py --batch-size 5
```

### 資料合併
```python
# 合併多個資料來源
import pandas as pd

# 讀取不同來源的資料
df1 = pd.read_csv('data/crawled/crawled_stock_data.csv')
df2 = pd.read_csv('data/scraped/scraped_stock_data.csv')

# 合併並去重
combined = pd.concat([df1, df2]).drop_duplicates(subset=['date', 'symbol'])
combined.to_csv('data/combined_stock_data.csv', index=False)
```

## 📞 技術支援

### 聯繫方式
- 查看專案 GitHub Issues
- 參考 API 官方文檔
- 檢查網路連接和防火牆設定

### 相關資源
- [新浪財經API文檔](https://finance.sina.com.cn/)
- [東方財富API文檔](https://www.eastmoney.com/)
- [Python requests 文檔](https://docs.python-requests.org/)

---

## 📝 總結

使用爬蟲腳本可以有效獲取缺失的股票資料，為Kronos回測提供完整的13個月資料支援。建議先使用簡化版本進行測試，確認可行後再使用完整版本進行大規模資料抓取。
