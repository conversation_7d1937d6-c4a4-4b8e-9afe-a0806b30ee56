"""
Data Verification Script for Kronos Backtesting
Verifies data availability and quality for the backtesting period.
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

# Ensure project root is in the Python path
sys.path.append("../")
from config import Config


class DataVerifier:
    """
    Verifies data availability and quality for backtesting.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.data_summary = {}
        
    def check_sample_data(self) -> Dict:
        """
        Check the available sample data and its characteristics.
        """
        data_path = "../examples/data/XSHG_5min_600977.csv"
        
        if not os.path.exists(data_path):
            return {
                'status': 'ERROR',
                'message': f'Sample data file not found at {data_path}'
            }
        
        try:
            # Load the data
            df = pd.read_csv(data_path)
            df['timestamps'] = pd.to_datetime(df['timestamps'])
            
            # Basic statistics
            start_date = df['timestamps'].min()
            end_date = df['timestamps'].max()
            total_records = len(df)
            
            # Check for missing values
            missing_values = df.isnull().sum()
            
            # Check data frequency
            time_diffs = df['timestamps'].diff().dropna()
            most_common_freq = time_diffs.mode()[0] if len(time_diffs) > 0 else None
            
            # Convert to daily data for backtesting analysis
            df_daily = df.set_index('timestamps').resample('D').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min', 
                'close': 'last',
                'volume': 'sum',
                'amount': 'sum'
            }).dropna()
            
            daily_records = len(df_daily)
            daily_start = df_daily.index.min()
            daily_end = df_daily.index.max()
            
            # Check if data covers the backtesting period
            backtest_start = pd.to_datetime(self.config.backtest_time_range[0])
            backtest_end = pd.to_datetime(self.config.backtest_time_range[1])
            
            covers_backtest_period = (daily_start <= backtest_start) and (daily_end >= backtest_start)
            
            summary = {
                'status': 'SUCCESS',
                'file_path': data_path,
                'raw_data': {
                    'start_date': start_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'end_date': end_date.strftime('%Y-%m-%d %H:%M:%S'),
                    'total_records': total_records,
                    'frequency': str(most_common_freq),
                    'missing_values': missing_values.to_dict()
                },
                'daily_data': {
                    'start_date': daily_start.strftime('%Y-%m-%d'),
                    'end_date': daily_end.strftime('%Y-%m-%d'),
                    'total_days': daily_records,
                    'covers_backtest_period': covers_backtest_period
                },
                'backtest_period': {
                    'requested_start': self.config.backtest_time_range[0],
                    'requested_end': self.config.backtest_time_range[1],
                    'data_available_from': daily_start.strftime('%Y-%m-%d'),
                    'data_available_until': daily_end.strftime('%Y-%m-%d')
                }
            }
            
            return summary
            
        except Exception as e:
            return {
                'status': 'ERROR',
                'message': f'Error reading data: {str(e)}'
            }
    
    def check_data_quality(self, data_path: str) -> Dict:
        """
        Perform detailed data quality checks.
        """
        try:
            df = pd.read_csv(data_path)
            df['timestamps'] = pd.to_datetime(df['timestamps'])
            
            quality_checks = {
                'price_consistency': self._check_price_consistency(df),
                'volume_validity': self._check_volume_validity(df),
                'temporal_consistency': self._check_temporal_consistency(df),
                'outlier_detection': self._detect_outliers(df)
            }
            
            return {
                'status': 'SUCCESS',
                'quality_checks': quality_checks
            }
            
        except Exception as e:
            return {
                'status': 'ERROR',
                'message': f'Error in quality check: {str(e)}'
            }
    
    def _check_price_consistency(self, df: pd.DataFrame) -> Dict:
        """Check if OHLC prices are consistent."""
        issues = []
        
        # Check if high >= max(open, close) and low <= min(open, close)
        high_issues = (df['high'] < df[['open', 'close']].max(axis=1)).sum()
        low_issues = (df['low'] > df[['open', 'close']].min(axis=1)).sum()
        
        # Check if high >= low
        high_low_issues = (df['high'] < df['low']).sum()
        
        if high_issues > 0:
            issues.append(f"{high_issues} records where high < max(open, close)")
        if low_issues > 0:
            issues.append(f"{low_issues} records where low > min(open, close)")
        if high_low_issues > 0:
            issues.append(f"{high_low_issues} records where high < low")
        
        return {
            'passed': len(issues) == 0,
            'issues': issues,
            'total_records': len(df)
        }
    
    def _check_volume_validity(self, df: pd.DataFrame) -> Dict:
        """Check volume and amount validity."""
        issues = []
        
        # Check for negative volumes
        negative_volume = (df['volume'] < 0).sum()
        negative_amount = (df['amount'] < 0).sum()
        
        # Check for zero volumes (might be valid during non-trading hours)
        zero_volume = (df['volume'] == 0).sum()
        
        if negative_volume > 0:
            issues.append(f"{negative_volume} records with negative volume")
        if negative_amount > 0:
            issues.append(f"{negative_amount} records with negative amount")
        
        return {
            'passed': len(issues) == 0,
            'issues': issues,
            'zero_volume_records': zero_volume,
            'total_records': len(df)
        }
    
    def _check_temporal_consistency(self, df: pd.DataFrame) -> Dict:
        """Check temporal consistency of the data."""
        issues = []
        
        # Check for duplicate timestamps
        duplicate_timestamps = df['timestamps'].duplicated().sum()
        
        # Check for proper chronological order
        is_sorted = df['timestamps'].is_monotonic_increasing
        
        if duplicate_timestamps > 0:
            issues.append(f"{duplicate_timestamps} duplicate timestamps")
        if not is_sorted:
            issues.append("Timestamps are not in chronological order")
        
        return {
            'passed': len(issues) == 0,
            'issues': issues,
            'is_chronological': is_sorted,
            'duplicate_timestamps': duplicate_timestamps
        }
    
    def _detect_outliers(self, df: pd.DataFrame) -> Dict:
        """Detect potential outliers in price and volume data."""
        outliers = {}
        
        for col in ['open', 'high', 'low', 'close', 'volume', 'amount']:
            if col in df.columns:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outlier_count = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
                outliers[col] = {
                    'count': outlier_count,
                    'percentage': (outlier_count / len(df)) * 100,
                    'lower_bound': lower_bound,
                    'upper_bound': upper_bound
                }
        
        return outliers
    
    def generate_data_report(self) -> str:
        """
        Generate a comprehensive data verification report.
        """
        print("="*60)
        print("KRONOS BACKTESTING DATA VERIFICATION REPORT")
        print("="*60)
        
        # Check sample data
        print("\n1. SAMPLE DATA AVAILABILITY")
        print("-" * 30)
        data_check = self.check_sample_data()
        
        if data_check['status'] == 'ERROR':
            print(f"❌ {data_check['message']}")
            return "Data verification failed"
        
        print(f"✅ Sample data found: {data_check['file_path']}")
        print(f"📊 Raw data: {data_check['raw_data']['total_records']} records")
        print(f"📅 Period: {data_check['raw_data']['start_date']} to {data_check['raw_data']['end_date']}")
        print(f"⏱️  Frequency: {data_check['raw_data']['frequency']}")
        print(f"📈 Daily data: {data_check['daily_data']['total_days']} trading days")
        print(f"📅 Daily period: {data_check['daily_data']['start_date']} to {data_check['daily_data']['end_date']}")
        
        # Check backtest period coverage
        print(f"\n2. BACKTESTING PERIOD COVERAGE")
        print("-" * 30)
        if data_check['daily_data']['covers_backtest_period']:
            print("✅ Data covers the requested backtesting period")
        else:
            print("⚠️  Data does not fully cover the requested backtesting period")
        
        print(f"📅 Requested: {data_check['backtest_period']['requested_start']} to {data_check['backtest_period']['requested_end']}")
        print(f"📅 Available: {data_check['backtest_period']['data_available_from']} to {data_check['backtest_period']['data_available_until']}")
        
        # Data quality checks
        print(f"\n3. DATA QUALITY ASSESSMENT")
        print("-" * 30)
        quality_check = self.check_data_quality("../examples/data/XSHG_5min_600977.csv")
        
        if quality_check['status'] == 'SUCCESS':
            for check_name, check_result in quality_check['quality_checks'].items():
                if isinstance(check_result, dict) and 'passed' in check_result:
                    status = "✅" if check_result['passed'] else "❌"
                    print(f"{status} {check_name.replace('_', ' ').title()}")
                    if not check_result['passed'] and 'issues' in check_result:
                        for issue in check_result['issues']:
                            print(f"   - {issue}")
        
        print(f"\n4. RECOMMENDATIONS")
        print("-" * 30)
        print("📝 For production backtesting:")
        print("   - Use CSI 300 constituent data from multiple sources")
        print("   - Ensure data covers the full backtesting period (July 2024 onwards)")
        print("   - Implement proper data cleaning and validation")
        print("   - Consider survivorship bias in historical constituent lists")
        print("   - Use proper benchmark data for performance comparison")
        
        print(f"\n5. NEXT STEPS")
        print("-" * 30)
        print("🚀 Ready to run simplified backtesting with available sample data")
        print("📊 Run: python finetune/simple_backtest.py")
        
        return "Data verification completed successfully"


def main():
    """Main function to run data verification."""
    config = Config()
    verifier = DataVerifier(config)
    
    # Generate comprehensive report
    result = verifier.generate_data_report()
    print(f"\n{result}")


if __name__ == '__main__':
    main()
