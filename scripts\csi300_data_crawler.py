#!/usr/bin/env python3
"""
CSI 300 成分股資料爬蟲 - 獲取2024年7月起的真實資料
CSI 300 Constituent Data Crawler - Fetch real data from July 2024
"""

import urllib.request
import urllib.parse
import json
import csv
import os
import time
from datetime import datetime, timedelta

class CSI300DataCrawler:
    """CSI 300成分股資料爬蟲"""
    
    def __init__(self):
        self.start_date = "2024-07-01"  # 2024年7月起
        self.end_date = "2024-12-31"    # 到年底
        self.output_dir = "data/csi300_raw"
        
        # 確保輸出目錄存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 請求頭
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
        
        # CSI 300 重要成分股（前50大權重股）
        self.csi300_top50 = [
            # 權重前10
            ('1.600519', '600519', 'SH', '貴州茅台'),
            ('0.000858', '000858', 'SZ', '五糧液'),
            ('1.600036', '600036', 'SH', '招商銀行'),
            ('0.000001', '000001', 'SZ', '平安銀行'),
            ('1.600000', '600000', 'SH', '浦發銀行'),
            ('1.601318', '601318', 'SH', '中國平安'),
            ('0.000002', '000002', 'SZ', '萬科A'),
            ('1.600887', '600887', 'SH', '伊利股份'),
            ('0.000063', '000063', 'SZ', '中興通訊'),
            ('1.601628', '601628', 'SH', '中國人壽'),
            
            # 權重11-20
            ('0.002415', '002415', 'SZ', '海康威視'),
            ('1.600016', '600016', 'SH', '民生銀行'),
            ('0.000895', '000895', 'SZ', '雙匯發展'),
            ('1.600999', '600999', 'SH', '招商證券'),
            ('0.000100', '000100', 'SZ', 'TCL科技'),
            ('1.600276', '600276', 'SH', '恆瑞醫藥'),
            ('0.000596', '000596', 'SZ', '古井貢酒'),
            ('1.600585', '600585', 'SH', '海螺水泥'),
            ('0.002304', '002304', 'SZ', '洋河股份'),
            ('1.601166', '601166', 'SH', '興業銀行'),
            
            # 權重21-30
            ('0.000858', '000858', 'SZ', '五糧液'),
            ('1.600030', '600030', 'SH', '中信證券'),
            ('0.002142', '002142', 'SZ', '寧波銀行'),
            ('1.600104', '600104', 'SH', '上汽集團'),
            ('0.000776', '000776', 'SZ', '廣發證券'),
            ('1.600048', '600048', 'SH', '保利發展'),
            ('0.000725', '000725', 'SZ', '京東方A'),
            ('1.600309', '600309', 'SH', '萬華化學'),
            ('0.002027', '002027', 'SZ', '分眾傳媒'),
            ('1.601012', '601012', 'SH', '隆基綠能'),
            
            # 權重31-40
            ('0.000338', '000338', 'SZ', '濰柴動力'),
            ('1.600519', '600519', 'SH', '貴州茅台'),
            ('0.002594', '002594', 'SZ', '比亞迪'),
            ('1.600690', '600690', 'SH', '海爾智家'),
            ('0.000568', '000568', 'SZ', '瀘州老窖'),
            ('1.600741', '600741', 'SH', '華域汽車'),
            ('0.002230', '002230', 'SZ', '科大訊飛'),
            ('1.600196', '600196', 'SH', '復星醫藥'),
            ('0.000157', '000157', 'SZ', '中聯重科'),
            ('1.600703', '600703', 'SH', '三安光電'),
            
            # 權重41-50
            ('0.000166', '000166', 'SZ', '申萬宏源'),
            ('1.600837', '600837', 'SH', '海通證券'),
            ('0.002008', '002008', 'SZ', '大族激光'),
            ('1.600660', '600660', 'SH', '福耀玻璃'),
            ('0.000069', '000069', 'SZ', '華僑城A'),
            ('1.600893', '600893', 'SH', '航發動力'),
            ('0.002352', '002352', 'SZ', '順豐控股'),
            ('1.600438', '600438', 'SH', '通威股份'),
            ('0.000983', '000983', 'SZ', '西山煤電'),
            ('1.600900', '600900', 'SH', '長江電力')
        ]
    
    def fetch_stock_data(self, secid, stock_code, market, stock_name):
        """獲取單隻股票的日K線資料"""
        try:
            print(f"📈 正在獲取 {stock_code}.{market} ({stock_name})...")
            
            # 構建東方財富API URL
            base_url = "http://push2his.eastmoney.com/api/qt/stock/kline/get"
            params = {
                'secid': secid,
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'fields1': 'f1,f2,f3,f4,f5,f6',
                'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                'klt': '101',  # 日K線
                'fqt': '1',    # 前復權
                'beg': self.start_date.replace('-', ''),
                'end': self.end_date.replace('-', ''),
                'lmt': '1000'
            }
            
            url = base_url + "?" + urllib.parse.urlencode(params)
            
            # 創建請求
            req = urllib.request.Request(url)
            for key, value in self.headers.items():
                req.add_header(key, value)
            
            # 發送請求
            with urllib.request.urlopen(req, timeout=15) as response:
                if response.status == 200:
                    data = json.loads(response.read().decode('utf-8'))
                    
                    if 'data' in data and data['data'] and 'klines' in data['data']:
                        klines = data['data']['klines']
                        
                        stock_data = []
                        for kline in klines:
                            try:
                                parts = kline.split(',')
                                if len(parts) >= 6:
                                    # 確保日期在目標範圍內
                                    date_str = parts[0]
                                    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                                    start_obj = datetime.strptime(self.start_date, '%Y-%m-%d')
                                    
                                    if date_obj >= start_obj:
                                        stock_data.append({
                                            'date': date_str,
                                            'symbol': f"{stock_code}.{market}",
                                            'name': stock_name,
                                            'open': float(parts[1]),
                                            'close': float(parts[2]),
                                            'high': float(parts[3]),
                                            'low': float(parts[4]),
                                            'volume': float(parts[5]),
                                            'amount': float(parts[6]) if len(parts) > 6 else float(parts[5]) * float(parts[2]),
                                            'turnover': float(parts[10]) if len(parts) > 10 else 0.0  # 換手率
                                        })
                            except (ValueError, IndexError):
                                continue
                        
                        if stock_data:
                            print(f"✅ {stock_code}.{market} 成功獲取 {len(stock_data)} 條記錄")
                            return stock_data
                        else:
                            print(f"⚠️ {stock_code}.{market} 沒有符合日期範圍的資料")
                    else:
                        print(f"⚠️ {stock_code}.{market} API返回空資料")
                else:
                    print(f"❌ {stock_code}.{market} HTTP錯誤: {response.status}")
        
        except Exception as e:
            print(f"❌ {stock_code}.{market} 獲取失敗: {e}")
        
        return []
    
    def save_data_to_csv(self, all_data, filename):
        """儲存資料到CSV檔案"""
        if not all_data:
            print("❌ 沒有資料可儲存")
            return False
        
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['date', 'symbol', 'name', 'open', 'high', 'low', 'close', 'volume', 'amount', 'turnover']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for row in all_data:
                    writer.writerow(row)
            
            print(f"✅ 資料已儲存到: {filepath}")
            return True
            
        except Exception as e:
            print(f"❌ 儲存資料失敗: {e}")
            return False
    
    def run_crawler(self):
        """執行CSI 300資料爬取"""
        print("="*80)
        print("🕷️ CSI 300 成分股資料爬取")
        print("="*80)
        print(f"📅 目標期間: {self.start_date} ~ {self.end_date}")
        print(f"📊 目標股票: {len(self.csi300_top50)} 隻 CSI 300 成分股")
        print(f"📁 輸出目錄: {self.output_dir}")
        print("-"*80)
        
        all_data = []
        success_count = 0
        failed_stocks = []
        
        for i, (secid, stock_code, market, stock_name) in enumerate(self.csi300_top50, 1):
            print(f"\n進度: {i}/{len(self.csi300_top50)}")
            
            try:
                stock_data = self.fetch_stock_data(secid, stock_code, market, stock_name)
                
                if stock_data:
                    all_data.extend(stock_data)
                    success_count += 1
                else:
                    failed_stocks.append(f"{stock_code}.{market}")
                
                # 避免請求過於頻繁
                if i % 5 == 0:
                    print(f"⏸️ 已處理 {i} 隻股票，休息3秒...")
                    time.sleep(3)
                else:
                    time.sleep(1)
                    
            except Exception as e:
                print(f"❌ 處理 {stock_code}.{market} 時發生錯誤: {e}")
                failed_stocks.append(f"{stock_code}.{market}")
                continue
        
        # 處理結果
        if all_data:
            # 按日期和股票代碼排序
            all_data.sort(key=lambda x: (x['date'], x['symbol']))
            
            # 儲存資料
            success = self.save_data_to_csv(all_data, "csi300_daily_data.csv")
            
            if success:
                # 統計資訊
                print(f"\n{'='*80}")
                print("📊 CSI 300 資料爬取結果")
                print("="*80)
                print(f"✅ 成功處理: {success_count}/{len(self.csi300_top50)} 隻股票")
                print(f"📈 總記錄數: {len(all_data)}")
                
                # 計算日期範圍
                dates = [item['date'] for item in all_data]
                if dates:
                    print(f"📅 實際資料期間: {min(dates)} ~ {max(dates)}")
                
                # 計算每隻股票的資料量
                symbol_counts = {}
                for item in all_data:
                    symbol = item['symbol']
                    symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
                
                if symbol_counts:
                    avg_records = sum(symbol_counts.values()) / len(symbol_counts)
                    print(f"📊 平均每隻股票: {avg_records:.0f} 條記錄")
                    print(f"📊 成功獲取資料的股票數: {len(symbol_counts)}")
                
                # 處理失敗的股票
                if failed_stocks:
                    print(f"\n❌ 獲取失敗的股票:")
                    for symbol in failed_stocks:
                        print(f"   {symbol}")
                    
                    # 儲存失敗清單
                    failed_file = os.path.join(self.output_dir, "failed_stocks.txt")
                    with open(failed_file, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(failed_stocks))
                    print(f"📝 失敗清單已儲存到: {failed_file}")
                
                print(f"\n🎉 CSI 300 資料爬取完成！")
                print(f"📋 下一步:")
                print(f"1. 驗證資料品質: python scripts/validate_csi300_data.py")
                print(f"2. 執行Kronos回測: python finetune/kronos_backtest.py")
                
                return True
        
        print(f"\n❌ 爬取失敗，沒有獲取到任何資料")
        return False

def main():
    """主函數"""
    crawler = CSI300DataCrawler()
    
    try:
        print("🚀 開始獲取CSI 300成分股資料...")
        success = crawler.run_crawler()
        
        if success:
            print(f"\n✅ CSI 300資料獲取任務成功完成！")
        else:
            print(f"\n❌ CSI 300資料獲取任務失敗！")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 用戶中斷獲取任務")
    except Exception as e:
        print(f"\n💥 獲取過程中發生錯誤: {e}")

if __name__ == "__main__":
    main()
