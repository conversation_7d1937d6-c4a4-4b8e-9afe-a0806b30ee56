#!/usr/bin/env python3
"""
直接爬蟲腳本 - 使用標準庫獲取股票資料
Direct Crawler Script - Using standard library to fetch stock data
"""

import urllib.request
import urllib.parse
import json
import csv
import os
import time
from datetime import datetime, timedelta

class DirectStockCrawler:
    """使用標準庫的直接股票爬蟲"""
    
    def __init__(self):
        self.start_date = "2024-08-30"
        self.end_date = "2025-07-31"
        self.output_dir = "data/direct_crawled"
        
        # 確保輸出目錄存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 請求頭
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://finance.sina.com.cn/'
        }
        
        # 重點股票清單 (CSI300核心成分股)
        self.target_stocks = [
            # 銀行股
            ('600000', 'SH', '浦發銀行'),
            ('600036', 'SH', '招商銀行'),
            ('000001', 'SZ', '平安銀行'),
            ('600016', 'SH', '民生銀行'),
            
            # 白酒股
            ('600519', 'SH', '貴州茅台'),
            ('000858', 'SZ', '五糧液'),
            ('000596', 'SZ', '古井貢酒'),
            
            # 科技股
            ('000002', 'SZ', '萬科A'),
            ('000063', 'SZ', '中興通訊'),
            ('002415', 'SZ', '海康威視'),
            
            # 保險股
            ('601318', 'SH', '中國平安'),
            ('601628', 'SH', '中國人壽'),
            
            # 其他重要股票
            ('600887', 'SH', '伊利股份'),
            ('000895', 'SZ', '雙匯發展'),
            ('600999', 'SH', '招商證券'),
            ('000100', 'SZ', 'TCL科技')
        ]
    
    def fetch_eastmoney_data(self, stock_code, market, stock_name):
        """從東方財富獲取資料"""
        try:
            print(f"📈 正在獲取 {stock_code}.{market} ({stock_name}) 資料...")
            
            # 構建東方財富API URL
            if market == 'SH':
                secid = f"1.{stock_code}"
            else:  # SZ
                secid = f"0.{stock_code}"
            
            # API參數
            params = {
                'secid': secid,
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'fields1': 'f1,f2,f3,f4,f5,f6',
                'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                'klt': '101',  # 日K線
                'fqt': '1',    # 前復權
                'beg': self.start_date.replace('-', ''),
                'end': self.end_date.replace('-', ''),
                'lmt': '1000'
            }
            
            # 構建URL
            base_url = "http://push2his.eastmoney.com/api/qt/stock/kline/get"
            url = base_url + "?" + urllib.parse.urlencode(params)
            
            # 創建請求
            req = urllib.request.Request(url)
            for key, value in self.headers.items():
                req.add_header(key, value)
            
            # 發送請求
            with urllib.request.urlopen(req, timeout=15) as response:
                if response.status == 200:
                    data = json.loads(response.read().decode('utf-8'))
                    
                    if 'data' in data and data['data'] and 'klines' in data['data']:
                        klines = data['data']['klines']
                        
                        stock_data = []
                        for kline in klines:
                            try:
                                parts = kline.split(',')
                                if len(parts) >= 6:
                                    stock_data.append({
                                        'date': parts[0],
                                        'symbol': f"{stock_code}.{market}",
                                        'name': stock_name,
                                        'open': float(parts[1]),
                                        'close': float(parts[2]),
                                        'high': float(parts[3]),
                                        'low': float(parts[4]),
                                        'volume': float(parts[5]),
                                        'amount': float(parts[6]) if len(parts) > 6 else float(parts[5]) * float(parts[2])
                                    })
                            except (ValueError, IndexError):
                                continue
                        
                        if stock_data:
                            print(f"✅ {stock_code}.{market} 成功獲取 {len(stock_data)} 條記錄")
                            return stock_data
                        else:
                            print(f"⚠️ {stock_code}.{market} 資料解析失敗")
                    else:
                        print(f"⚠️ {stock_code}.{market} API返回空資料")
                else:
                    print(f"❌ {stock_code}.{market} HTTP錯誤: {response.status}")
            
        except Exception as e:
            print(f"❌ {stock_code}.{market} 獲取失敗: {e}")
        
        return []
    
    def fetch_sina_data(self, stock_code, market, stock_name):
        """從新浪財經獲取資料"""
        try:
            print(f"📊 嘗試新浪財經 {stock_code}.{market} ({stock_name})...")
            
            # 構建新浪財經股票代碼
            sina_code = f"{market.lower()}{stock_code}"
            
            # API參數
            params = {
                'symbol': sina_code,
                'scale': '240',  # 日線
                'ma': 'no',
                'datalen': '500'  # 獲取500個交易日
            }
            
            # 構建URL
            base_url = "https://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData"
            url = base_url + "?" + urllib.parse.urlencode(params)
            
            # 創建請求
            req = urllib.request.Request(url)
            for key, value in self.headers.items():
                req.add_header(key, value)
            
            # 發送請求
            with urllib.request.urlopen(req, timeout=15) as response:
                if response.status == 200:
                    response_text = response.read().decode('utf-8')
                    
                    if response_text and response_text != 'null':
                        data = json.loads(response_text)
                        
                        if data and isinstance(data, list):
                            stock_data = []
                            for item in data:
                                try:
                                    date = datetime.strptime(item['day'], '%Y-%m-%d')
                                    start_date = datetime.strptime(self.start_date, '%Y-%m-%d')
                                    end_date = datetime.strptime(self.end_date, '%Y-%m-%d')
                                    
                                    # 只保留目標期間的資料
                                    if start_date <= date <= end_date:
                                        stock_data.append({
                                            'date': item['day'],
                                            'symbol': f"{stock_code}.{market}",
                                            'name': stock_name,
                                            'open': float(item['open']),
                                            'high': float(item['high']),
                                            'low': float(item['low']),
                                            'close': float(item['close']),
                                            'volume': float(item['volume']),
                                            'amount': float(item['volume']) * float(item['close'])
                                        })
                                except (KeyError, ValueError, TypeError):
                                    continue
                            
                            if stock_data:
                                print(f"✅ {stock_code}.{market} 新浪成功獲取 {len(stock_data)} 條記錄")
                                return stock_data
            
        except Exception as e:
            print(f"❌ {stock_code}.{market} 新浪獲取失敗: {e}")
        
        return []
    
    def crawl_single_stock(self, stock_code, market, stock_name):
        """爬取單隻股票資料"""
        # 嘗試東方財富API
        data = self.fetch_eastmoney_data(stock_code, market, stock_name)
        
        if not data:
            # 如果東方財富失敗，嘗試新浪財經
            time.sleep(1)  # 避免請求過於頻繁
            data = self.fetch_sina_data(stock_code, market, stock_name)
        
        return data
    
    def save_data_to_csv(self, all_data, filename):
        """儲存資料到CSV檔案"""
        if not all_data:
            print("❌ 沒有資料可儲存")
            return False
        
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['date', 'symbol', 'name', 'open', 'high', 'low', 'close', 'volume', 'amount']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for row in all_data:
                    writer.writerow(row)
            
            print(f"✅ 資料已儲存到: {filepath}")
            return True
            
        except Exception as e:
            print(f"❌ 儲存資料失敗: {e}")
            return False
    
    def run_direct_crawling(self):
        """執行直接爬蟲"""
        print("="*80)
        print("🕷️ 直接爬蟲 - 獲取真實股票資料")
        print("="*80)
        print(f"📅 目標期間: {self.start_date} ~ {self.end_date}")
        print(f"📊 目標股票: {len(self.target_stocks)} 隻")
        print(f"📁 輸出目錄: {self.output_dir}")
        print("-"*80)
        
        all_data = []
        success_count = 0
        failed_stocks = []
        
        for i, (stock_code, market, stock_name) in enumerate(self.target_stocks, 1):
            print(f"\n進度: {i}/{len(self.target_stocks)}")
            
            try:
                stock_data = self.crawl_single_stock(stock_code, market, stock_name)
                
                if stock_data:
                    all_data.extend(stock_data)
                    success_count += 1
                else:
                    failed_stocks.append(f"{stock_code}.{market}")
                
                # 每3隻股票休息一下，避免被封IP
                if i % 3 == 0:
                    print(f"⏸️ 已處理 {i} 隻股票，休息2秒...")
                    time.sleep(2)
                else:
                    time.sleep(0.5)  # 短暫延遲
                
            except Exception as e:
                print(f"❌ 處理 {stock_code}.{market} 時發生錯誤: {e}")
                failed_stocks.append(f"{stock_code}.{market}")
                continue
        
        # 處理結果
        if all_data:
            # 按日期和股票代碼排序
            all_data.sort(key=lambda x: (x['symbol'], x['date']))
            
            # 儲存資料
            success = self.save_data_to_csv(all_data, "real_stock_data.csv")
            
            if success:
                # 統計資訊
                print(f"\n{'='*80}")
                print("📊 爬取結果統計")
                print("="*80)
                print(f"✅ 成功處理: {success_count}/{len(self.target_stocks)} 隻股票")
                print(f"📈 總記錄數: {len(all_data)}")
                
                # 計算日期範圍
                dates = [item['date'] for item in all_data]
                if dates:
                    print(f"📅 實際資料期間: {min(dates)} ~ {max(dates)}")
                
                # 計算每隻股票的資料量
                symbol_counts = {}
                for item in all_data:
                    symbol = item['symbol']
                    symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
                
                if symbol_counts:
                    avg_records = sum(symbol_counts.values()) / len(symbol_counts)
                    print(f"📊 平均每隻股票: {avg_records:.0f} 條記錄")
                    
                    # 顯示成功的股票
                    print(f"\n✅ 成功獲取資料的股票:")
                    for symbol, count in symbol_counts.items():
                        print(f"   {symbol}: {count} 條記錄")
                
                # 顯示失敗的股票
                if failed_stocks:
                    print(f"\n❌ 獲取失敗的股票:")
                    for symbol in failed_stocks:
                        print(f"   {symbol}")
                    
                    # 儲存失敗清單
                    failed_file = os.path.join(self.output_dir, "failed_stocks.txt")
                    with open(failed_file, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(failed_stocks))
                    print(f"📝 失敗清單已儲存到: {failed_file}")
                
                print(f"\n🎉 真實資料爬取完成！")
                print(f"📋 下一步:")
                print(f"1. 驗證資料品質: python scripts/validate_extended_data.py")
                print(f"2. 執行回測: python finetune/standalone_backtest.py")
                
                return True
        
        print(f"\n❌ 爬取失敗，沒有獲取到任何真實資料")
        print(f"🔧 建議:")
        print(f"1. 檢查網路連接")
        print(f"2. 確認API服務可用性")
        print(f"3. 稍後重試")
        
        return False

def main():
    """主函數"""
    crawler = DirectStockCrawler()
    
    try:
        print("🚀 開始執行真實資料爬取...")
        success = crawler.run_direct_crawling()
        
        if success:
            print(f"\n✅ 真實資料爬取任務成功完成！")
        else:
            print(f"\n❌ 真實資料爬取任務失敗！")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 用戶中斷爬取任務")
    except Exception as e:
        print(f"\n💥 爬取過程中發生錯誤: {e}")

if __name__ == "__main__":
    main()
