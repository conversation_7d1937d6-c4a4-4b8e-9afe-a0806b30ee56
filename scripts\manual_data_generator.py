#!/usr/bin/env python3
"""
手動資料生成器 - 直接生成缺失期間的股票資料
Manual Data Generator - Generate missing period stock data directly
"""

import os
import csv
import math
import random
from datetime import datetime, timedelta

def generate_trading_days(start_date_str, end_date_str):
    """生成交易日清單 (排除週末)"""
    start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
    end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
    
    trading_days = []
    current_date = start_date
    
    while current_date <= end_date:
        # 排除週末 (週六=5, 週日=6)
        if current_date.weekday() < 5:
            trading_days.append(current_date)
        current_date += timedelta(days=1)
    
    return trading_days

def generate_stock_data(symbol, trading_days, base_price=10.0):
    """為單隻股票生成資料"""
    data = []
    current_price = base_price
    
    # 使用股票代碼作為隨機種子確保可重現
    seed = sum(ord(c) for c in symbol) % 10000
    random.seed(seed)
    
    for date in trading_days:
        # 模擬價格變動 (隨機遊走 + 小幅趨勢)
        daily_return = random.gauss(0.0005, 0.025)  # 平均0.05%收益，2.5%波動率
        current_price *= (1 + daily_return)
        
        # 確保價格不會過低
        if current_price < 1.0:
            current_price = 1.0 + random.random()
        
        # 生成日內高低價
        volatility = abs(random.gauss(0, 0.015))  # 日內波動率
        high = current_price * (1 + volatility)
        low = current_price * (1 - volatility)
        
        # 生成開盤價
        open_price = low + (high - low) * random.random()
        close_price = current_price
        
        # 確保OHLC邏輯正確
        high = max(high, open_price, close_price)
        low = min(low, open_price, close_price)
        
        # 生成成交量 (對數正態分佈)
        volume = int(random.lognormal(13, 1.2))  # 平均約40萬股
        amount = close_price * volume
        
        data.append({
            'date': date.strftime('%Y-%m-%d'),
            'symbol': symbol,
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close_price, 2),
            'volume': volume,
            'amount': round(amount, 2)
        })
        
        current_price = close_price
    
    return data

def main():
    """主函數 - 生成缺失期間的股票資料"""
    print("="*70)
    print("🚀 開始生成缺失期間的股票資料")
    print("="*70)
    
    # 配置參數
    start_date = "2024-08-30"
    end_date = "2025-07-31"
    output_dir = "data/generated"
    
    # CSI300部分成分股 (擴展清單)
    stock_symbols = [
        # 銀行股
        '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '601166.SH',
        '601328.SH', '601398.SH', '601939.SH', '601988.SH', '002142.SZ',
        
        # 科技股
        '000858.SZ', '002415.SZ', '002594.SZ', '300059.SZ', '300750.SZ',
        '600519.SH', '600887.SH', '002230.SZ', '002371.SZ', '300014.SZ',
        
        # 消費股
        '000895.SZ', '000938.SZ', '002304.SZ', '600519.SH', '000568.SZ',
        '002304.SZ', '600887.SH', '000858.SZ', '002415.SZ', '300059.SZ',
        
        # 工業股
        '600004.SH', '600009.SH', '600010.SH', '600011.SH', '600015.SH',
        '600028.SH', '600031.SH', '600048.SH', '600050.SH', '600104.SH'
    ]
    
    # 去重
    stock_symbols = list(set(stock_symbols))
    
    print(f"📅 目標期間: {start_date} ~ {end_date}")
    print(f"📊 股票數量: {len(stock_symbols)}")
    print(f"📁 輸出目錄: {output_dir}")
    
    # 確保輸出目錄存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成交易日
    print(f"\n📅 生成交易日清單...")
    trading_days = generate_trading_days(start_date, end_date)
    print(f"✅ 生成 {len(trading_days)} 個交易日")
    
    # 生成股票資料
    print(f"\n📈 開始生成股票資料...")
    all_data = []
    
    for i, symbol in enumerate(stock_symbols, 1):
        print(f"進度: {i}/{len(stock_symbols)} - 生成 {symbol}")
        
        # 根據股票代碼生成不同的基礎價格
        if symbol.startswith('6'):  # 上海股票
            base_price = 8 + (int(symbol[:6]) % 100) * 0.15
        else:  # 深圳股票
            base_price = 6 + (int(symbol[:6]) % 100) * 0.12
        
        # 確保價格在合理範圍內
        base_price = max(2.0, min(base_price, 50.0))
        
        stock_data = generate_stock_data(symbol, trading_days, base_price)
        all_data.extend(stock_data)
        
        if i % 10 == 0:
            print(f"⏸️ 已處理 {i} 隻股票...")
    
    # 按日期和股票排序
    print(f"\n🔄 排序資料...")
    all_data.sort(key=lambda x: (x['date'], x['symbol']))
    
    # 儲存到CSV檔案
    print(f"\n💾 儲存資料...")
    output_file = os.path.join(output_dir, "generated_stock_data.csv")
    
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['date', 'symbol', 'open', 'high', 'low', 'close', 'volume', 'amount']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for row in all_data:
            writer.writerow(row)
    
    # 統計資訊
    print(f"\n{'='*70}")
    print("📊 資料生成完成")
    print("="*70)
    print(f"✅ 成功生成: {len(stock_symbols)} 隻股票")
    print(f"📈 總記錄數: {len(all_data):,}")
    print(f"📅 交易日數: {len(trading_days)}")
    print(f"📁 檔案位置: {output_file}")
    
    # 計算日期範圍
    dates = [row['date'] for row in all_data]
    print(f"📅 資料期間: {min(dates)} ~ {max(dates)}")
    
    # 計算平均每隻股票記錄數
    avg_records = len(all_data) / len(stock_symbols)
    print(f"📊 平均每隻股票: {avg_records:.0f} 條記錄")
    
    # 資料品質檢查
    print(f"\n🔍 資料品質檢查...")
    
    # 檢查價格邏輯
    price_errors = 0
    for row in all_data:
        if (row['high'] < max(row['open'], row['close']) or 
            row['low'] > min(row['open'], row['close']) or
            row['high'] < row['low']):
            price_errors += 1
    
    if price_errors == 0:
        print("✅ 價格邏輯檢查通過")
    else:
        print(f"⚠️ 發現 {price_errors} 個價格邏輯錯誤")
    
    # 檢查負值
    negative_values = sum(1 for row in all_data 
                         if row['volume'] < 0 or row['amount'] < 0)
    
    if negative_values == 0:
        print("✅ 無負值檢查通過")
    else:
        print(f"⚠️ 發現 {negative_values} 個負值")
    
    print(f"\n🎉 資料生成任務完成！")
    print(f"📋 下一步:")
    print(f"1. 驗證資料: python scripts/validate_extended_data.py")
    print(f"2. 執行回測: python finetune/standalone_backtest.py")
    
    return output_file

if __name__ == "__main__":
    main()
