#!/usr/bin/env python3
"""
Kronos 回測引擎 - 實現論文中的Top-K投資組合策略
Kronos Backtesting Engine - Implementing Top-K Portfolio Strategy from the paper
"""

import os
import sys
import csv
import math
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

# 添加專案根目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

class KronosBacktester:
    """
    Kronos回測引擎
    實現論文中的Top-K投資組合策略
    """
    
    def __init__(self):
        # 策略參數 (按照論文設定)
        self.K = 50  # CSI 300: 選取前50檔股票
        self.min_hold_days = 5  # 每檔至少持有5天
        self.trading_cost = 0.0015  # 0.15%交易成本
        self.initial_capital = 100_000_000  # 1億初始資金
        
        # 信號生成參數
        self.H = 10  # 預測區間：10天 horizon (固定)
        self.lookback_window = 90  # 回溯視窗：90天
        
        # 回測結果
        self.results = {
            'dates': [],
            'portfolio_values': [],
            'daily_returns': [],
            'positions': [],
            'turnover': [],
            'cumulative_returns': []
        }
        
        # 持倉追蹤
        self.current_positions = {}  # {symbol: weight}
        self.position_hold_days = {}  # {symbol: days_held}
        
    def load_csi300_data(self, data_file: str) -> Dict[str, List[Dict]]:
        """載入CSI 300資料"""
        print(f"📊 載入CSI 300資料: {data_file}")
        
        if not os.path.exists(data_file):
            raise FileNotFoundError(f"資料檔案不存在: {data_file}")
        
        stock_data = defaultdict(list)
        
        with open(data_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                symbol = row['symbol']
                stock_data[symbol].append({
                    'date': datetime.strptime(row['date'], '%Y-%m-%d'),
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': float(row['volume']),
                    'amount': float(row['amount']),
                    'turnover': float(row.get('turnover', 0.0))
                })
        
        # 按日期排序
        for symbol in stock_data:
            stock_data[symbol].sort(key=lambda x: x['date'])
        
        print(f"✅ 成功載入 {len(stock_data)} 隻股票的資料")
        return dict(stock_data)
    
    def generate_signals(self, stock_data: Dict[str, List[Dict]], current_date: datetime) -> Dict[str, float]:
        """
        生成預測信號
        
        Signal = (1/H) * Σ(PredictedPrice_{t+k}) - Price_t
        
        由於沒有實際的Kronos模型，這裡使用技術指標模擬信號
        """
        signals = {}
        
        for symbol, data in stock_data.items():
            try:
                # 找到當前日期的索引
                current_idx = None
                for i, record in enumerate(data):
                    if record['date'] == current_date:
                        current_idx = i
                        break
                
                if current_idx is None or current_idx < self.lookback_window:
                    continue
                
                # 獲取回溯視窗資料
                lookback_data = data[current_idx - self.lookback_window:current_idx + 1]
                current_price = lookback_data[-1]['close']
                
                # 模擬信號生成 (使用技術指標組合)
                signal = self._calculate_technical_signal(lookback_data, current_price)
                
                if not math.isnan(signal) and not math.isinf(signal):
                    signals[symbol] = signal
                    
            except Exception as e:
                print(f"⚠️ 生成 {symbol} 信號時發生錯誤: {e}")
                continue
        
        return signals
    
    def _calculate_technical_signal(self, data: List[Dict], current_price: float) -> float:
        """
        計算技術指標信號 (模擬Kronos預測)
        
        組合多個技術指標：
        1. 動量指標 (Momentum)
        2. 相對強弱指標 (RSI)
        3. 移動平均線 (MA)
        4. 波動率調整
        """
        try:
            prices = [record['close'] for record in data]
            volumes = [record['volume'] for record in data]
            
            # 1. 動量信號 (10日動量)
            if len(prices) >= 10:
                momentum = (prices[-1] - prices[-10]) / prices[-10]
            else:
                momentum = 0
            
            # 2. RSI信號
            rsi = self._calculate_rsi(prices, period=14)
            rsi_signal = (50 - rsi) / 50  # 轉換為信號 (-1 to 1)
            
            # 3. 移動平均信號
            ma_short = sum(prices[-5:]) / 5 if len(prices) >= 5 else current_price
            ma_long = sum(prices[-20:]) / 20 if len(prices) >= 20 else current_price
            ma_signal = (ma_short - ma_long) / ma_long if ma_long > 0 else 0
            
            # 4. 成交量信號
            avg_volume = sum(volumes[-20:]) / 20 if len(volumes) >= 20 else volumes[-1]
            volume_signal = min((volumes[-1] / avg_volume - 1), 1) if avg_volume > 0 else 0
            
            # 5. 波動率調整
            if len(prices) >= 20:
                returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
                volatility = math.sqrt(sum(r**2 for r in returns[-20:]) / 20)
                vol_adjustment = 1 / (1 + volatility * 10)  # 降低高波動股票的信號
            else:
                vol_adjustment = 1
            
            # 組合信號 (加權平均)
            combined_signal = (
                0.3 * momentum +
                0.2 * rsi_signal +
                0.3 * ma_signal +
                0.2 * volume_signal
            ) * vol_adjustment
            
            # 模擬H=10天的預測 (添加一些隨機性)
            import random
            random.seed(hash(str(current_price)) % 2**32)  # 基於價格的確定性隨機
            prediction_noise = random.gauss(0, 0.01)  # 1%的預測噪音
            
            final_signal = combined_signal + prediction_noise
            
            return final_signal
            
        except Exception as e:
            print(f"⚠️ 計算技術信號時發生錯誤: {e}")
            return 0.0
    
    def _calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """計算相對強弱指標 (RSI)"""
        if len(prices) < period + 1:
            return 50  # 中性值
        
        gains = []
        losses = []
        
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(-change)
        
        if len(gains) < period:
            return 50
        
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def execute_topk_strategy(self, signals: Dict[str, float], current_date: datetime) -> Dict[str, float]:
        """
        執行Top-K策略
        
        1. 根據信號排序
        2. 選取前K=50檔股票
        3. 等權配置
        4. 考慮最小持有期限制
        """
        # 1. 根據信號排序，選取前K檔
        sorted_signals = sorted(signals.items(), key=lambda x: x[1], reverse=True)
        top_k_symbols = [symbol for symbol, signal in sorted_signals[:self.K]]
        
        # 2. 檢查最小持有期約束
        new_positions = {}
        
        # 保留必須持有的股票 (未滿最小持有期)
        for symbol, days_held in self.position_hold_days.items():
            if days_held < self.min_hold_days and symbol in self.current_positions:
                new_positions[symbol] = self.current_positions[symbol]
        
        # 計算剩餘可分配權重
        allocated_weight = sum(new_positions.values())
        remaining_weight = 1.0 - allocated_weight
        
        # 從Top-K中選擇新股票填補剩餘權重
        available_symbols = [s for s in top_k_symbols if s not in new_positions]
        
        if available_symbols and remaining_weight > 0:
            weight_per_stock = remaining_weight / len(available_symbols)
            for symbol in available_symbols:
                new_positions[symbol] = weight_per_stock
        
        # 3. 確保總權重為1
        total_weight = sum(new_positions.values())
        if total_weight > 0:
            for symbol in new_positions:
                new_positions[symbol] /= total_weight
        
        return new_positions
    
    def calculate_portfolio_return(self, old_positions: Dict[str, float], 
                                 new_positions: Dict[str, float],
                                 stock_data: Dict[str, List[Dict]], 
                                 current_date: datetime) -> Tuple[float, float]:
        """計算投資組合收益和換手率"""
        portfolio_return = 0.0
        turnover = 0.0
        
        # 計算股票收益
        for symbol, weight in old_positions.items():
            try:
                # 找到當前和前一日的價格
                symbol_data = stock_data.get(symbol, [])
                current_price = None
                prev_price = None
                
                for i, record in enumerate(symbol_data):
                    if record['date'] == current_date:
                        current_price = record['close']
                        if i > 0:
                            prev_price = symbol_data[i-1]['close']
                        break
                
                if current_price and prev_price and prev_price > 0:
                    stock_return = (current_price - prev_price) / prev_price
                    portfolio_return += weight * stock_return
                    
            except Exception as e:
                print(f"⚠️ 計算 {symbol} 收益時發生錯誤: {e}")
                continue
        
        # 計算換手率
        old_symbols = set(old_positions.keys())
        new_symbols = set(new_positions.keys())
        
        # 賣出的股票
        sold_symbols = old_symbols - new_symbols
        turnover += sum(old_positions.get(symbol, 0) for symbol in sold_symbols)
        
        # 買入的股票
        bought_symbols = new_symbols - old_symbols
        turnover += sum(new_positions.get(symbol, 0) for symbol in bought_symbols)
        
        # 調整倉位的股票
        adjusted_symbols = old_symbols & new_symbols
        for symbol in adjusted_symbols:
            weight_change = abs(new_positions.get(symbol, 0) - old_positions.get(symbol, 0))
            turnover += weight_change
        
        # 應用交易成本
        trading_cost = turnover * self.trading_cost
        portfolio_return -= trading_cost
        
        return portfolio_return, turnover
    
    def run_backtest(self, data_file: str) -> Dict:
        """執行完整回測"""
        print("="*80)
        print("🚀 Kronos Top-K 投資組合策略回測")
        print("="*80)
        print(f"📊 策略參數:")
        print(f"   - Top-K: {self.K} 檔股票")
        print(f"   - 最小持有期: {self.min_hold_days} 天")
        print(f"   - 交易成本: {self.trading_cost*100:.2f}%")
        print(f"   - 預測區間: {self.H} 天")
        print(f"   - 回溯視窗: {self.lookback_window} 天")
        print("-"*80)
        
        # 載入資料
        stock_data = self.load_csi300_data(data_file)
        
        if not stock_data:
            raise ValueError("沒有載入到任何股票資料")
        
        # 獲取所有交易日期
        all_dates = set()
        for symbol_data in stock_data.values():
            for record in symbol_data:
                all_dates.add(record['date'])
        
        trading_dates = sorted(list(all_dates))
        
        # 確保有足夠的歷史資料
        start_idx = self.lookback_window
        if start_idx >= len(trading_dates):
            raise ValueError(f"歷史資料不足，需要至少 {self.lookback_window} 天")
        
        print(f"📅 回測期間: {trading_dates[start_idx].date()} ~ {trading_dates[-1].date()}")
        print(f"📊 總交易日數: {len(trading_dates) - start_idx}")
        
        # 初始化
        portfolio_value = self.initial_capital
        
        # 開始回測
        for i in range(start_idx, len(trading_dates)):
            current_date = trading_dates[i]
            
            # 生成信號
            signals = self.generate_signals(stock_data, current_date)
            
            if not signals:
                print(f"⚠️ {current_date.date()} 沒有生成任何信號")
                continue
            
            # 執行Top-K策略
            new_positions = self.execute_topk_strategy(signals, current_date)
            
            # 計算收益和換手率
            if self.current_positions:  # 不是第一天
                daily_return, turnover = self.calculate_portfolio_return(
                    self.current_positions, new_positions, stock_data, current_date
                )
                
                portfolio_value *= (1 + daily_return)
                
                # 記錄結果
                self.results['dates'].append(current_date)
                self.results['portfolio_values'].append(portfolio_value)
                self.results['daily_returns'].append(daily_return)
                self.results['positions'].append(dict(new_positions))
                self.results['turnover'].append(turnover)
                self.results['cumulative_returns'].append(portfolio_value / self.initial_capital - 1)
            
            # 更新持倉
            self.current_positions = new_positions
            
            # 更新持有天數
            for symbol in self.current_positions:
                if symbol in self.position_hold_days:
                    self.position_hold_days[symbol] += 1
                else:
                    self.position_hold_days[symbol] = 1
            
            # 移除已賣出股票的持有天數記錄
            symbols_to_remove = [s for s in self.position_hold_days if s not in self.current_positions]
            for symbol in symbols_to_remove:
                del self.position_hold_days[symbol]
            
            # 進度顯示
            if i % 20 == 0:
                progress = (i - start_idx) / (len(trading_dates) - start_idx) * 100
                print(f"📈 回測進度: {progress:.1f}% - {current_date.date()}")
        
        # 計算績效指標
        metrics = self.calculate_performance_metrics()
        
        print(f"\n{'='*80}")
        print("📊 回測結果")
        print("="*80)
        for metric, value in metrics.items():
            if isinstance(value, float):
                if 'Return' in metric or 'Ratio' in metric:
                    print(f"{metric}: {value:.4f} ({value*100:.2f}%)")
                else:
                    print(f"{metric}: {value:.4f}")
            else:
                print(f"{metric}: {value}")
        
        return {
            'results': self.results,
            'metrics': metrics,
            'final_positions': self.current_positions
        }
    
    def calculate_performance_metrics(self) -> Dict:
        """計算績效指標"""
        if not self.results['daily_returns']:
            return {}
        
        returns = self.results['daily_returns']
        
        # 基本指標
        total_return = self.results['cumulative_returns'][-1] if self.results['cumulative_returns'] else 0
        num_days = len(returns)
        
        # 年化收益率 (AER)
        annualized_return = (1 + total_return) ** (252 / num_days) - 1 if num_days > 0 else 0
        
        # 波動率
        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        daily_volatility = math.sqrt(variance)
        annualized_volatility = daily_volatility * math.sqrt(252)
        
        # 夏普比率 / 資訊比率 (IR)
        sharpe_ratio = mean_return / daily_volatility * math.sqrt(252) if daily_volatility > 0 else 0
        
        # 最大回撤
        cumulative_returns = self.results['cumulative_returns']
        running_max = cumulative_returns[0] if cumulative_returns else 0
        max_drawdown = 0
        
        for cum_ret in cumulative_returns:
            running_max = max(running_max, cum_ret)
            drawdown = (cum_ret - running_max) / (1 + running_max) if running_max > -1 else 0
            max_drawdown = min(max_drawdown, drawdown)
        
        # 平均換手率
        avg_turnover = sum(self.results['turnover']) / len(self.results['turnover']) if self.results['turnover'] else 0
        
        return {
            'Total Return': total_return,
            'Annualized Excess Return (AER)': annualized_return,
            'Annualized Volatility': annualized_volatility,
            'Sharpe Ratio': sharpe_ratio,
            'Information Ratio (IR)': sharpe_ratio,  # 簡化為夏普比率
            'Max Drawdown': max_drawdown,
            'Average Daily Turnover': avg_turnover,
            'Number of Trading Days': num_days
        }

def main():
    """主函數"""
    # 資料檔案路徑
    data_files = [
        "../data/csi300_raw/csi300_daily_data.csv",
        "../data/real_crawled/real_stock_data.csv",
        "../examples/data/XSHG_5min_600977.csv"
    ]
    
    data_file = None
    for file_path in data_files:
        if os.path.exists(file_path):
            data_file = file_path
            break
    
    if not data_file:
        print("❌ 找不到資料檔案，請先執行資料爬取:")
        print("   python scripts/csi300_data_crawler.py")
        return
    
    # 執行回測
    backtester = KronosBacktester()
    
    try:
        results = backtester.run_backtest(data_file)
        print(f"\n🎉 Kronos回測完成！")
        
    except Exception as e:
        print(f"\n❌ 回測失敗: {e}")

if __name__ == "__main__":
    main()
