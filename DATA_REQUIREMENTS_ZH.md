# Kronos 回測資料需求指南 (2024-07 ~ 2025-07)

## 📊 資料需求概述

### 目標回測期間
- **開始日期**: 2024年7月1日
- **結束日期**: 2025年7月31日  
- **總期間**: 13個月
- **資料頻率**: 日頻資料 (推薦) 或分鐘級資料

### 當前資料狀況
- **現有資料**: 2024年6月18日 ~ 2024年8月29日
- **資料格式**: 5分鐘頻率 OHLCV 資料
- **缺失期間**: 2024年8月30日 ~ 2025年7月31日

## 🎯 所需資料範圍

### 1. 標的範圍
根據論文方法論，需要以下資料：

**主要標的 (CSI 300 成分股)**:
- 中證300指數成分股清單
- 每日OHLCV資料
- 成分股變動歷史

**基準資料**:
- CSI 300 指數日線資料
- 無風險利率資料 (用於計算超額收益)

### 2. 資料欄位要求
```csv
date,symbol,open,high,low,close,volume,amount
2024-07-01,000001.SZ,10.50,10.60,10.45,10.55,1000000,10550000
2024-07-01,000002.SZ,15.20,15.35,15.10,15.30,800000,12240000
...
```

**必要欄位**:
- `date`: 交易日期 (YYYY-MM-DD)
- `symbol`: 股票代碼 (如: 000001.SZ, 600000.SH)
- `open`: 開盤價
- `high`: 最高價  
- `low`: 最低價
- `close`: 收盤價
- `volume`: 成交量
- `amount`: 成交金額

## 📈 資料來源建議

### 1. 免費資料源
```python
# 使用 Qlib 獲取中國A股資料
import qlib
from qlib.data import D

# 初始化 Qlib
qlib.init(provider_uri="~/.qlib/qlib_data/cn_data", region="CN")

# 下載資料
python scripts/get_data.py qlib_data --target_dir ~/.qlib/qlib_data/cn_data --region cn
```

### 2. 商業資料源
- **Wind**: 萬得金融終端
- **Bloomberg**: 彭博終端
- **Tushare Pro**: 中國股票資料API
- **聚寬 (JoinQuant)**: 量化交易平台

### 3. 開源資料源
- **AKShare**: 免費的中國股票資料
- **Yahoo Finance**: 部分中國股票資料
- **東方財富**: 公開資料介面

## 🔧 資料獲取腳本

讓我創建一個資料獲取腳本範例：

### 使用 AKShare 獲取資料
```python
import akshare as ak
import pandas as pd
from datetime import datetime, timedelta

def get_csi300_data(start_date="2024-07-01", end_date="2025-07-31"):
    """獲取CSI300成分股資料"""
    
    # 獲取CSI300成分股清單
    csi300_stocks = ak.index_stock_cons(index="000300")
    
    all_data = []
    
    for _, stock in csi300_stocks.iterrows():
        symbol = stock['品種代碼']
        try:
            # 獲取個股日線資料
            stock_data = ak.stock_zh_a_hist(
                symbol=symbol, 
                period="daily",
                start_date=start_date.replace("-", ""),
                end_date=end_date.replace("-", ""),
                adjust=""
            )
            
            stock_data['symbol'] = symbol
            all_data.append(stock_data)
            
        except Exception as e:
            print(f"獲取 {symbol} 資料失敗: {e}")
    
    # 合併所有資料
    combined_data = pd.concat(all_data, ignore_index=True)
    return combined_data

# 使用範例
data = get_csi300_data()
data.to_csv("csi300_data_2024_2025.csv", index=False)
```

## 📋 資料驗證清單

### 1. 資料完整性檢查
- [ ] 涵蓋完整的2024-07-01到2025-07-31期間
- [ ] 包含所有CSI300成分股
- [ ] 無缺失的交易日
- [ ] OHLCV資料完整

### 2. 資料品質檢查
- [ ] 價格資料邏輯一致性 (high >= max(open,close), low <= min(open,close))
- [ ] 成交量和成交額非負
- [ ] 無異常的價格跳躍
- [ ] 時間序列連續性

### 3. 格式標準化
- [ ] 統一的日期格式
- [ ] 統一的股票代碼格式
- [ ] 統一的數值精度
- [ ] 統一的檔案編碼 (UTF-8)

## 🚀 實施步驟

### 步驟 1: 資料獲取
```bash
# 安裝必要套件
pip install akshare qlib tushare

# 執行資料獲取腳本
python scripts/download_csi300_data.py
```

### 步驟 2: 資料預處理
```bash
# 執行資料清理和驗證
python finetune/data_preprocessing.py
```

### 步驟 3: 更新回測配置
```bash
# 更新配置檔案中的資料路徑
# 確認回測期間設定正確
```

### 步驟 4: 執行回測
```bash
# 執行完整回測
python finetune/standalone_backtest.py
```

## ⚠️ 注意事項

### 1. 資料品質
- 確保資料來源的可靠性
- 注意除權除息調整
- 處理停牌和退市股票

### 2. 成分股變動
- CSI300成分股會定期調整
- 需要歷史成分股清單
- 避免前瞻偏差 (Look-ahead bias)

### 3. 存活偏差
- 考慮已退市股票
- 使用當時的成分股清單
- 避免只使用現存股票

### 4. 資料儲存
- 建議使用資料庫儲存大量資料
- 考慮資料壓縮和索引
- 定期備份重要資料

## 📞 技術支援

如需協助獲取資料或遇到技術問題，請參考：
- Qlib 官方文檔: https://qlib.readthedocs.io/
- AKShare 文檔: https://akshare.akfamily.xyz/
- 本專案的 GitHub Issues

## 📊 預期結果

完成資料獲取後，您將能夠：
- 執行完整的13個月回測
- 獲得更可靠的績效指標
- 進行季節性分析
- 比較不同市場環境下的表現
