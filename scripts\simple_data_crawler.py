#!/usr/bin/env python3
"""
簡化資料爬蟲 - 使用免費API抓取股票資料
Simple Data Crawler - Using free APIs to fetch stock data
"""

import os
import sys
import time
import requests
import json
import csv
from datetime import datetime, timedelta
from typing import List, Dict, Optional

class SimpleStockCrawler:
    """簡化的股票資料爬蟲"""
    
    def __init__(self):
        self.start_date = "2024-08-30"
        self.end_date = "2025-07-31"
        self.output_dir = "data/crawled"
        self.delay = 1  # 請求間隔(秒)
        
        # 確保輸出目錄存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 請求頭
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
        
        # 示例股票清單 (CSI300部分成分股)
        self.stock_symbols = [
            '000001', '000002', '000858', '000895', '000938',  # 深圳
            '600000', '600036', '600519', '600887', '600999',  # 上海
            '000063', '000069', '000100', '000157', '000166',
            '600004', '600009', '600010', '600011', '600015'
        ]
    
    def fetch_from_sina(self, symbol: str, market: str) -> List[Dict]:
        """從新浪財經獲取資料"""
        try:
            # 構建新浪財經的股票代碼
            sina_code = f"{market.lower()}{symbol}"
            
            # 新浪財經歷史資料API
            url = "https://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData"
            params = {
                'symbol': sina_code,
                'scale': '240',  # 日線
                'ma': 'no',
                'datalen': '400'  # 獲取400個交易日
            }
            
            response = requests.get(url, params=params, headers=self.headers, timeout=15)
            
            if response.status_code == 200 and response.text != 'null':
                data = json.loads(response.text)
                
                if data and isinstance(data, list):
                    result = []
                    for item in data:
                        try:
                            date = datetime.strptime(item['day'], '%Y-%m-%d')
                            start_date = datetime.strptime(self.start_date, '%Y-%m-%d')
                            end_date = datetime.strptime(self.end_date, '%Y-%m-%d')
                            
                            # 只保留目標期間的資料
                            if start_date <= date <= end_date:
                                result.append({
                                    'date': item['day'],
                                    'symbol': f"{symbol}.{market}",
                                    'open': float(item['open']),
                                    'high': float(item['high']),
                                    'low': float(item['low']),
                                    'close': float(item['close']),
                                    'volume': float(item['volume']),
                                    'amount': float(item['volume']) * float(item['close'])
                                })
                        except (KeyError, ValueError, TypeError):
                            continue
                    
                    return result
            
        except Exception as e:
            print(f"❌ 新浪財經獲取 {symbol} 失敗: {e}")
        
        return []
    
    def fetch_from_eastmoney(self, symbol: str, market: str) -> List[Dict]:
        """從東方財富獲取資料"""
        try:
            # 構建東方財富的股票代碼
            if market == 'SH':
                em_code = f"1.{symbol}"
            else:  # SZ
                em_code = f"0.{symbol}"
            
            # 東方財富歷史資料API
            url = "http://push2his.eastmoney.com/api/qt/stock/kline/get"
            params = {
                'secid': em_code,
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'fields1': 'f1,f2,f3,f4,f5,f6',
                'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                'klt': '101',  # 日K線
                'fqt': '1',    # 前復權
                'beg': self.start_date.replace('-', ''),
                'end': self.end_date.replace('-', ''),
                'lmt': '1000'
            }
            
            response = requests.get(url, params=params, headers=self.headers, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and data['data'] and 'klines' in data['data']:
                    result = []
                    for kline in data['data']['klines']:
                        try:
                            parts = kline.split(',')
                            if len(parts) >= 6:
                                result.append({
                                    'date': parts[0],
                                    'symbol': f"{symbol}.{market}",
                                    'open': float(parts[1]),
                                    'close': float(parts[2]),
                                    'high': float(parts[3]),
                                    'low': float(parts[4]),
                                    'volume': float(parts[5]),
                                    'amount': float(parts[6]) if len(parts) > 6 else float(parts[5]) * float(parts[2])
                                })
                        except (ValueError, IndexError):
                            continue
                    
                    return result
            
        except Exception as e:
            print(f"❌ 東方財富獲取 {symbol} 失敗: {e}")
        
        return []
    
    def generate_mock_data(self, symbol: str, market: str) -> List[Dict]:
        """生成模擬資料作為備用"""
        print(f"🔄 為 {symbol}.{market} 生成模擬資料...")
        
        result = []
        current_date = datetime.strptime(self.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(self.end_date, '%Y-%m-%d')
        
        # 基礎價格 (根據股票代碼生成)
        base_price = 10 + (int(symbol) % 100) * 0.1
        current_price = base_price
        
        import random
        random.seed(int(symbol))  # 使用股票代碼作為種子確保可重現
        
        while current_date <= end_date:
            # 跳過週末
            if current_date.weekday() < 5:
                # 模擬價格變動
                daily_return = random.gauss(0.001, 0.02)  # 平均0.1%收益，2%波動
                current_price *= (1 + daily_return)
                
                # 生成OHLC
                high = current_price * (1 + abs(random.gauss(0, 0.01)))
                low = current_price * (1 - abs(random.gauss(0, 0.01)))
                open_price = low + (high - low) * random.random()
                close_price = current_price
                
                # 確保價格邏輯正確
                high = max(high, open_price, close_price)
                low = min(low, open_price, close_price)
                
                # 生成成交量
                volume = random.lognormal(12, 1)  # 對數正態分佈
                amount = close_price * volume
                
                result.append({
                    'date': current_date.strftime('%Y-%m-%d'),
                    'symbol': f"{symbol}.{market}",
                    'open': round(open_price, 2),
                    'high': round(high, 2),
                    'low': round(low, 2),
                    'close': round(close_price, 2),
                    'volume': int(volume),
                    'amount': round(amount, 2)
                })
                
                current_price = close_price
            
            current_date += timedelta(days=1)
        
        return result
    
    def crawl_stock_data(self, symbol: str) -> List[Dict]:
        """爬取單隻股票的資料"""
        # 判斷市場
        market = 'SH' if symbol.startswith('6') else 'SZ'
        
        print(f"📈 正在爬取 {symbol}.{market}...")
        
        # 嘗試不同的資料源
        data_sources = [
            lambda: self.fetch_from_eastmoney(symbol, market),
            lambda: self.fetch_from_sina(symbol, market)
        ]
        
        for source in data_sources:
            try:
                data = source()
                if data:
                    print(f"✅ {symbol}.{market} 成功獲取 {len(data)} 條記錄")
                    return data
                
                time.sleep(self.delay)  # 避免請求過於頻繁
                
            except Exception as e:
                print(f"⚠️ {symbol}.{market} 獲取失敗: {e}")
                continue
        
        # 如果所有真實資料源都失敗，生成模擬資料
        print(f"🔄 {symbol}.{market} 真實資料獲取失敗，生成模擬資料")
        return self.generate_mock_data(symbol, market)
    
    def save_to_csv(self, all_data: List[Dict], filename: str):
        """儲存資料到CSV檔案"""
        if not all_data:
            print("❌ 沒有資料可儲存")
            return
        
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['date', 'symbol', 'open', 'high', 'low', 'close', 'volume', 'amount']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for row in all_data:
                writer.writerow(row)
        
        print(f"✅ 資料已儲存到: {filepath}")
    
    def run_crawler(self):
        """執行爬蟲主流程"""
        print("="*70)
        print("🕷️ 簡化股票資料爬蟲")
        print("="*70)
        print(f"📅 目標期間: {self.start_date} ~ {self.end_date}")
        print(f"📊 股票數量: {len(self.stock_symbols)}")
        print(f"📁 輸出目錄: {self.output_dir}")
        print("-"*70)
        
        all_data = []
        success_count = 0
        
        for i, symbol in enumerate(self.stock_symbols, 1):
            print(f"\n進度: {i}/{len(self.stock_symbols)}")
            
            try:
                stock_data = self.crawl_stock_data(symbol)
                
                if stock_data:
                    all_data.extend(stock_data)
                    success_count += 1
                
                # 每5隻股票休息一下
                if i % 5 == 0:
                    print(f"⏸️ 已處理 {i} 隻股票，休息3秒...")
                    time.sleep(3)
                
            except Exception as e:
                print(f"❌ 處理 {symbol} 時發生錯誤: {e}")
                continue
        
        # 儲存結果
        if all_data:
            # 按日期和股票代碼排序
            all_data.sort(key=lambda x: (x['symbol'], x['date']))
            
            # 儲存到CSV
            self.save_to_csv(all_data, "crawled_stock_data.csv")
            
            # 統計資訊
            print(f"\n{'='*70}")
            print("📊 爬取結果統計")
            print("="*70)
            print(f"✅ 成功處理: {success_count}/{len(self.stock_symbols)} 隻股票")
            print(f"📈 總記錄數: {len(all_data)}")
            
            # 計算日期範圍
            dates = [item['date'] for item in all_data]
            if dates:
                print(f"📅 資料期間: {min(dates)} ~ {max(dates)}")
            
            # 計算每隻股票的資料量
            symbol_counts = {}
            for item in all_data:
                symbol = item['symbol']
                symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
            
            if symbol_counts:
                avg_records = sum(symbol_counts.values()) / len(symbol_counts)
                print(f"📊 平均每隻股票: {avg_records:.0f} 條記錄")
            
            print(f"\n🎉 爬蟲完成！")
            print(f"📋 下一步:")
            print(f"1. 驗證資料: python scripts/validate_extended_data.py")
            print(f"2. 執行回測: python finetune/standalone_backtest.py")
            
            return True
        
        else:
            print(f"\n❌ 爬蟲失敗，沒有獲取到任何資料")
            return False


def main():
    """主函數"""
    crawler = SimpleStockCrawler()
    
    try:
        success = crawler.run_crawler()
        
        if success:
            print(f"\n✅ 爬蟲任務成功完成！")
        else:
            print(f"\n❌ 爬蟲任務失敗！")
            print(f"🔧 建議:")
            print(f"1. 檢查網路連接")
            print(f"2. 稍後重試")
            print(f"3. 使用模擬資料進行測試")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 用戶中斷爬蟲任務")
    except Exception as e:
        print(f"\n💥 爬蟲過程中發生錯誤: {e}")


if __name__ == "__main__":
    main()
