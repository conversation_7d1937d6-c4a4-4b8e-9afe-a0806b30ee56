"""
Standalone Backtesting Framework for Kronos
Implements the methodology described in the paper without external dependencies.
This version works independently and demonstrates the backtesting approach.
"""

import os
import csv
import math
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta


class StandaloneBacktester:
    """
    A standalone backtesting framework that implements the Top-K strategy
    as described in the Kronos paper without requiring external dependencies.
    """
    
    def __init__(self):
        # Configuration parameters (matching the paper methodology)
        self.backtest_n_symbol_hold = 50  # Number of symbols to hold (using 1 for demo)
        self.backtest_hold_thresh = 5  # Minimum holding period (days)
        self.trading_cost = 0.0015  # 0.15% trading cost per transaction
        self.initial_capital = 100_000_000  # 100M initial capital
        self.prediction_horizon = 10  # H days for prediction horizon
        self.signal_lookback = 90  # 90 days lookback window
        
    def load_csv_data(self, file_path: str) -> List[Dict]:
        """
        Load CSV data without pandas dependency.
        Support both original format and extended data format.
        """
        data = []

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Data file not found: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                try:
                    # Handle different date formats
                    if 'timestamps' in row:
                        # Original format with timestamps
                        timestamp = datetime.strptime(row['timestamps'], '%Y-%m-%d %H:%M:%S')
                        date_filter = timestamp >= datetime(2024, 7, 1)
                    elif 'date' in row:
                        # Extended format with date only
                        timestamp = datetime.strptime(row['date'], '%Y-%m-%d')
                        date_filter = timestamp >= datetime(2024, 7, 1)
                    else:
                        continue

                    # Filter for July 2024 onwards and until July 2025
                    if date_filter and timestamp <= datetime(2025, 7, 31):
                        data.append({
                            'timestamp': timestamp,
                            'symbol': row.get('symbol', 'DEFAULT'),
                            'open': float(row['open']),
                            'high': float(row['high']),
                            'low': float(row['low']),
                            'close': float(row['close']),
                            'volume': float(row['volume']),
                            'amount': float(row['amount'])
                        })
                except (ValueError, KeyError) as e:
                    print(f"Skipping invalid row: {e}")
                    continue

        print(f"Loaded {len(data)} records from July 2024 to July 2025")
        return data
    
    def convert_to_daily(self, intraday_data: List[Dict]) -> List[Dict]:
        """
        Convert intraday data to daily OHLCV data.
        """
        daily_data = {}
        
        for record in intraday_data:
            date = record['timestamp'].date()
            
            if date not in daily_data:
                daily_data[date] = {
                    'date': date,
                    'open': record['open'],
                    'high': record['high'],
                    'low': record['low'],
                    'close': record['close'],
                    'volume': record['volume'],
                    'amount': record['amount']
                }
            else:
                # Update daily aggregates
                daily_data[date]['high'] = max(daily_data[date]['high'], record['high'])
                daily_data[date]['low'] = min(daily_data[date]['low'], record['low'])
                daily_data[date]['close'] = record['close']  # Last close of the day
                daily_data[date]['volume'] += record['volume']
                daily_data[date]['amount'] += record['amount']
        
        # Convert to sorted list
        daily_list = list(daily_data.values())
        daily_list.sort(key=lambda x: x['date'])
        
        print(f"Converted to {len(daily_list)} daily records")
        return daily_list
    
    def generate_signals(self, daily_data: List[Dict]) -> Dict:
        """
        Generate mock prediction signals based on price momentum.
        In a real scenario, this would use the Kronos model.
        """
        signals = {}
        
        for i in range(len(daily_data) - self.prediction_horizon):
            current_price = daily_data[i]['close']
            
            # Look ahead to get actual future price (for demonstration)
            if i + self.prediction_horizon < len(daily_data):
                future_price = daily_data[i + self.prediction_horizon]['close']
                expected_return = (future_price - current_price) / current_price
                
                # Add some noise to simulate model uncertainty
                import random
                random.seed(42)  # For reproducible results
                signal = expected_return + random.gauss(0, 0.01)
                
                signals[daily_data[i]['date']] = signal
        
        print(f"Generated {len(signals)} prediction signals")
        return signals
    
    def run_topk_strategy(self, daily_data: List[Dict], signals: Dict) -> Dict:
        """
        Implement the Top-K strategy as described in the paper.
        For this demo with single stock, we use a simple long/short strategy.
        """
        results = {
            'dates': [],
            'returns': [],
            'positions': [],
            'cumulative_return': [],
            'portfolio_value': []
        }
        
        portfolio_value = self.initial_capital
        position = 0  # 0 = no position, 1 = long, -1 = short
        position_hold_days = 0
        
        for i in range(1, len(daily_data)):
            current_date = daily_data[i]['date']
            prev_date = daily_data[i-1]['date']
            
            current_price = daily_data[i]['close']
            prev_price = daily_data[i-1]['close']
            
            # Calculate stock return
            stock_return = (current_price - prev_price) / prev_price
            
            # Get signal for previous day (to avoid look-ahead bias)
            signal = signals.get(prev_date, 0)
            
            # Trading decision
            new_position = 0
            if signal > 0.01:  # Positive signal threshold
                new_position = 1  # Long position
            elif signal < -0.01:  # Negative signal threshold
                new_position = -1  # Short position
            
            # Apply minimum holding period constraint
            if position_hold_days < self.backtest_hold_thresh:
                new_position = position  # Keep current position
            
            # Calculate portfolio return
            portfolio_return = 0.0
            trading_cost_applied = 0.0
            
            if position != 0:
                # Apply position to get portfolio return
                portfolio_return = position * stock_return
            
            # Apply trading costs if position changes
            if new_position != position:
                trading_cost_applied = self.trading_cost
                portfolio_return -= trading_cost_applied
                position_hold_days = 0
            else:
                position_hold_days += 1
            
            # Update portfolio value
            portfolio_value *= (1 + portfolio_return)
            
            # Update position
            position = new_position
            
            # Store results
            results['dates'].append(current_date)
            results['returns'].append(portfolio_return)
            results['positions'].append(position)
            results['portfolio_value'].append(portfolio_value)
            results['cumulative_return'].append(portfolio_value / self.initial_capital - 1)
        
        return results
    
    def calculate_metrics(self, results: Dict) -> Dict:
        """
        Calculate performance metrics including AER and IR.
        """
        returns = results['returns']
        
        if not returns:
            return {'error': 'No returns to calculate metrics'}
        
        # Basic statistics
        total_return = results['cumulative_return'][-1] if results['cumulative_return'] else 0
        avg_daily_return = sum(returns) / len(returns)
        
        # Calculate volatility
        mean_return = avg_daily_return
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        daily_volatility = math.sqrt(variance)
        annualized_volatility = daily_volatility * math.sqrt(252)
        
        # Annualized return
        num_days = len(returns)
        annualized_return = (1 + total_return) ** (252 / num_days) - 1 if num_days > 0 else 0
        
        # Sharpe ratio (simplified as Information Ratio)
        sharpe_ratio = avg_daily_return / daily_volatility * math.sqrt(252) if daily_volatility > 0 else 0
        
        # Maximum drawdown
        cumulative_returns = results['cumulative_return']
        running_max = cumulative_returns[0]
        max_drawdown = 0
        
        for cum_ret in cumulative_returns:
            running_max = max(running_max, cum_ret + 1)
            drawdown = (cum_ret + 1) / running_max - 1
            max_drawdown = min(max_drawdown, drawdown)
        
        metrics = {
            'Total Return': total_return,
            'Annualized Return (AER)': annualized_return,
            'Annualized Volatility': annualized_volatility,
            'Sharpe Ratio': sharpe_ratio,
            'Information Ratio (IR)': sharpe_ratio,
            'Max Drawdown': max_drawdown,
            'Number of Trading Days': num_days,
            'Average Daily Return': avg_daily_return
        }
        
        return metrics
    
    def save_results(self, results: Dict, metrics: Dict, output_dir: str = "../outputs"):
        """
        Save results to CSV files.
        """
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Save daily results
        results_file = os.path.join(output_dir, "backtest_daily_results.csv")
        with open(results_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['Date', 'Daily_Return', 'Position', 'Portfolio_Value', 'Cumulative_Return'])
            
            for i in range(len(results['dates'])):
                writer.writerow([
                    results['dates'][i],
                    results['returns'][i],
                    results['positions'][i],
                    results['portfolio_value'][i],
                    results['cumulative_return'][i]
                ])
        
        # Save metrics
        metrics_file = os.path.join(output_dir, "backtest_metrics.csv")
        with open(metrics_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['Metric', 'Value'])
            for metric, value in metrics.items():
                writer.writerow([metric, value])
        
        print(f"Results saved to {output_dir}")
    
    def run_backtest(self, data_file: str = None):
        """
        Run the complete backtesting pipeline with extended data support.
        """
        print("="*60)
        print("KRONOS 擴展回測 (2024-07 ~ 2025-07)")
        print("KRONOS EXTENDED BACKTESTING (2024-07 ~ 2025-07)")
        print("="*60)
        print("Methodology: Top-K Strategy (Paper Implementation)")
        print(f"Backtesting Period: July 2024 to July 2025")
        print(f"Trading Cost: {self.trading_cost*100:.2f}%")
        print(f"Minimum Holding Period: {self.backtest_hold_thresh} days")
        print("-"*60)

        # Determine data file to use
        if data_file is None:
            # Try extended data first, then fall back to original
            extended_files = [
                "../data/extended/csi300_extended_data.csv",
                "../data/extended/csi300_qlib_data.csv",
                "../data/extended/synthetic_extended_data.csv",
                "../examples/data/XSHG_5min_600977.csv"
            ]

            data_file = None
            for file_path in extended_files:
                if os.path.exists(file_path):
                    data_file = file_path
                    print(f"📁 Using data file: {file_path}")
                    break

            if data_file is None:
                raise FileNotFoundError("No suitable data file found. Please run download_extended_data.py first.")

        # Load and process data
        print("\n1. Loading data...")
        raw_data = self.load_csv_data(data_file)

        if not raw_data:
            raise ValueError("No data loaded. Please check the data file and date range.")

        print("\n2. Converting to daily data...")
        daily_data = self.convert_to_daily(raw_data)

        print("\n3. Generating prediction signals...")
        signals = self.generate_signals(daily_data)

        print("\n4. Running Top-K strategy...")
        results = self.run_topk_strategy(daily_data, signals)

        print("\n5. Calculating performance metrics...")
        metrics = self.calculate_metrics(results)

        # Display results
        print("\n" + "="*60)
        print("回測結果 BACKTESTING RESULTS")
        print("="*60)
        for metric, value in metrics.items():
            if isinstance(value, float):
                if 'Return' in metric or 'Ratio' in metric:
                    print(f"{metric}: {value:.4f} ({value*100:.2f}%)")
                else:
                    print(f"{metric}: {value:.4f}")
            else:
                print(f"{metric}: {value}")

        # Save results
        print("\n6. Saving results...")
        self.save_results(results, metrics)

        print("\n✅ 回測完成！Backtesting completed successfully!")

        # Extended period specific notes
        data_period = f"{raw_data[0]['timestamp'].date()} to {raw_data[-1]['timestamp'].date()}"
        print(f"\n📅 實際資料期間 Actual data period: {data_period}")
        print(f"📊 總交易日數 Total trading days: {len(daily_data)}")

        if len(raw_data) < 1000:  # If using limited data
            print("\n⚠️  注意 Note: Using limited sample data.")
            print("For complete 13-month backtesting:")
            print("1. Run: python scripts/download_extended_data.py")
            print("2. Ensure CSI 300 constituent data coverage")
            print("3. Integrate actual Kronos model predictions")

        return results, metrics


def main():
    """Main function to run standalone backtesting."""
    backtester = StandaloneBacktester()
    results, metrics = backtester.run_backtest()
    return results, metrics


if __name__ == '__main__':
    main()
