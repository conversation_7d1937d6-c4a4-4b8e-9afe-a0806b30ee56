#!/usr/bin/env python3
"""
擴展資料下載腳本 - 獲取2024-07到2025-07的CSI300資料
Extended Data Download Script - Get CSI300 data from 2024-07 to 2025-07
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加專案根目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

class ExtendedDataDownloader:
    """擴展資料下載器"""
    
    def __init__(self):
        self.start_date = "2024-07-01"
        self.end_date = "2025-07-31"
        self.output_dir = "data/extended"
        
        # 確保輸出目錄存在
        os.makedirs(self.output_dir, exist_ok=True)
        
    def download_with_akshare(self):
        """使用 AKShare 下載資料"""
        try:
            import akshare as ak
            print("✅ 使用 AKShare 下載資料...")
            
            # 獲取CSI300成分股
            print("📋 獲取CSI300成分股清單...")
            csi300_stocks = ak.index_stock_cons(index="000300")
            print(f"📊 找到 {len(csi300_stocks)} 隻CSI300成分股")
            
            all_data = []
            failed_stocks = []
            
            for idx, stock in csi300_stocks.iterrows():
                symbol = stock['品種代碼']
                name = stock['品種名稱']
                
                try:
                    print(f"📈 下載 {symbol} ({name}) 資料...")
                    
                    # 獲取日線資料
                    stock_data = ak.stock_zh_a_hist(
                        symbol=symbol,
                        period="daily", 
                        start_date=self.start_date.replace("-", ""),
                        end_date=self.end_date.replace("-", ""),
                        adjust="qfq"  # 前復權
                    )
                    
                    if not stock_data.empty:
                        # 標準化欄位名稱
                        stock_data = stock_data.rename(columns={
                            '日期': 'date',
                            '開盤': 'open', 
                            '收盤': 'close',
                            '最高': 'high',
                            '最低': 'low',
                            '成交量': 'volume',
                            '成交額': 'amount'
                        })
                        
                        stock_data['symbol'] = symbol
                        stock_data['name'] = name
                        all_data.append(stock_data)
                        
                except Exception as e:
                    print(f"❌ 下載 {symbol} 失敗: {e}")
                    failed_stocks.append(symbol)
                    continue
            
            if all_data:
                # 合併所有資料
                print("🔄 合併資料...")
                combined_data = pd.concat(all_data, ignore_index=True)
                
                # 儲存資料
                output_file = os.path.join(self.output_dir, "csi300_extended_data.csv")
                combined_data.to_csv(output_file, index=False, encoding='utf-8')
                print(f"✅ 資料已儲存到: {output_file}")
                
                # 儲存失敗清單
                if failed_stocks:
                    failed_file = os.path.join(self.output_dir, "failed_downloads.txt")
                    with open(failed_file, 'w') as f:
                        f.write('\n'.join(failed_stocks))
                    print(f"⚠️  {len(failed_stocks)} 隻股票下載失敗，清單已儲存到: {failed_file}")
                
                return combined_data
            else:
                print("❌ 沒有成功下載任何資料")
                return None
                
        except ImportError:
            print("❌ AKShare 未安裝，請執行: pip install akshare")
            return None
        except Exception as e:
            print(f"❌ AKShare 下載失敗: {e}")
            return None
    
    def download_with_qlib(self):
        """使用 Qlib 下載資料"""
        try:
            import qlib
            from qlib.data import D
            from qlib.config import REG_CN
            
            print("✅ 使用 Qlib 下載資料...")
            
            # 初始化 Qlib
            qlib.init(provider_uri="~/.qlib/qlib_data/cn_data", region=REG_CN)
            
            # 獲取CSI300成分股
            instruments = D.instruments(market='csi300')
            print(f"📊 找到 {len(instruments)} 隻CSI300成分股")
            
            # 獲取資料
            fields = ['$open', '$high', '$low', '$close', '$volume', '$vwap']
            data = D.features(
                instruments=instruments,
                fields=fields,
                start_time=self.start_date,
                end_time=self.end_date
            )
            
            if not data.empty:
                # 重置索引並標準化欄位名稱
                data = data.reset_index()
                data = data.rename(columns={
                    'datetime': 'date',
                    'instrument': 'symbol',
                    '$open': 'open',
                    '$high': 'high', 
                    '$low': 'low',
                    '$close': 'close',
                    '$volume': 'volume',
                    '$vwap': 'vwap'
                })
                
                # 計算成交額 (近似)
                data['amount'] = data['close'] * data['volume']
                
                # 儲存資料
                output_file = os.path.join(self.output_dir, "csi300_qlib_data.csv")
                data.to_csv(output_file, index=False)
                print(f"✅ Qlib 資料已儲存到: {output_file}")
                
                return data
            else:
                print("❌ Qlib 沒有獲取到資料")
                return None
                
        except ImportError:
            print("❌ Qlib 未安裝，請執行: pip install qlib")
            return None
        except Exception as e:
            print(f"❌ Qlib 下載失敗: {e}")
            return None
    
    def generate_synthetic_data(self):
        """生成模擬資料 (用於測試)"""
        print("🔄 生成模擬資料用於測試...")
        
        # 讀取現有資料作為基礎
        existing_file = "examples/data/XSHG_5min_600977.csv"
        if not os.path.exists(existing_file):
            print(f"❌ 找不到基礎資料檔案: {existing_file}")
            return None
        
        # 讀取現有資料
        base_data = pd.read_csv(existing_file)
        base_data['timestamps'] = pd.to_datetime(base_data['timestamps'])
        
        # 轉換為日線資料
        daily_data = base_data.set_index('timestamps').resample('D').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min', 
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum'
        }).dropna()
        
        # 獲取最後一個交易日的資料
        last_date = daily_data.index[-1]
        last_close = daily_data['close'].iloc[-1]
        
        print(f"📅 基礎資料最後日期: {last_date.date()}")
        print(f"💰 最後收盤價: {last_close:.2f}")
        
        # 生成擴展期間的日期
        start_extend = last_date + timedelta(days=1)
        end_extend = pd.to_datetime(self.end_date)
        
        # 生成交易日 (排除週末)
        date_range = pd.date_range(start=start_extend, end=end_extend, freq='B')
        
        print(f"🔄 生成 {len(date_range)} 個交易日的模擬資料...")
        
        # 生成多隻股票的模擬資料
        symbols = ['600000.SH', '000001.SZ', '000002.SZ', '600036.SH', '600519.SH']
        all_synthetic_data = []
        
        np.random.seed(42)  # 確保可重現
        
        for symbol in symbols:
            synthetic_data = []
            current_price = last_close * (0.8 + 0.4 * np.random.random())  # 隨機起始價格
            
            for date in date_range:
                # 模擬價格變動 (隨機遊走 + 趨勢)
                daily_return = np.random.normal(0.001, 0.02)  # 平均0.1%日收益，2%波動率
                current_price *= (1 + daily_return)
                
                # 生成OHLC
                high = current_price * (1 + abs(np.random.normal(0, 0.01)))
                low = current_price * (1 - abs(np.random.normal(0, 0.01)))
                open_price = low + (high - low) * np.random.random()
                close_price = current_price
                
                # 確保價格邏輯正確
                high = max(high, open_price, close_price)
                low = min(low, open_price, close_price)
                
                # 生成成交量
                volume = np.random.lognormal(13, 1)  # 對數正態分佈
                amount = close_price * volume
                
                synthetic_data.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'symbol': symbol,
                    'open': round(open_price, 2),
                    'high': round(high, 2),
                    'low': round(low, 2), 
                    'close': round(close_price, 2),
                    'volume': int(volume),
                    'amount': round(amount, 2)
                })
                
                current_price = close_price
            
            all_synthetic_data.extend(synthetic_data)
        
        # 轉換為DataFrame
        synthetic_df = pd.DataFrame(all_synthetic_data)
        
        # 儲存模擬資料
        output_file = os.path.join(self.output_dir, "synthetic_extended_data.csv")
        synthetic_df.to_csv(output_file, index=False)
        print(f"✅ 模擬資料已儲存到: {output_file}")
        
        return synthetic_df
    
    def validate_data(self, data):
        """驗證資料品質"""
        if data is None or data.empty:
            print("❌ 沒有資料可供驗證")
            return False
        
        print("🔍 驗證資料品質...")
        
        # 基本統計
        print(f"📊 資料形狀: {data.shape}")
        print(f"📅 日期範圍: {data['date'].min()} 到 {data['date'].max()}")
        
        if 'symbol' in data.columns:
            print(f"📈 股票數量: {data['symbol'].nunique()}")
        
        # 檢查缺失值
        missing_data = data.isnull().sum()
        if missing_data.sum() > 0:
            print("⚠️  發現缺失值:")
            print(missing_data[missing_data > 0])
        else:
            print("✅ 無缺失值")
        
        # 檢查價格邏輯
        if all(col in data.columns for col in ['open', 'high', 'low', 'close']):
            price_errors = 0
            price_errors += (data['high'] < data[['open', 'close']].max(axis=1)).sum()
            price_errors += (data['low'] > data[['open', 'close']].min(axis=1)).sum()
            price_errors += (data['high'] < data['low']).sum()
            
            if price_errors > 0:
                print(f"⚠️  發現 {price_errors} 個價格邏輯錯誤")
            else:
                print("✅ 價格邏輯正確")
        
        return True
    
    def run(self):
        """執行資料下載流程"""
        print("="*60)
        print("🚀 開始下載擴展回測資料 (2024-07 ~ 2025-07)")
        print("="*60)
        
        # 嘗試不同的資料來源
        data = None
        
        # 1. 嘗試 AKShare
        data = self.download_with_akshare()
        
        # 2. 如果失敗，嘗試 Qlib
        if data is None:
            data = self.download_with_qlib()
        
        # 3. 如果都失敗，生成模擬資料
        if data is None:
            print("⚠️  真實資料下載失敗，生成模擬資料用於測試...")
            data = self.generate_synthetic_data()
        
        # 驗證資料
        if data is not None:
            self.validate_data(data)
            print("\n✅ 資料下載完成！")
            print(f"📁 資料儲存位置: {self.output_dir}")
            print("\n📋 下一步:")
            print("1. 檢查下載的資料品質")
            print("2. 更新回測配置檔案")
            print("3. 執行擴展回測: python finetune/standalone_backtest.py")
        else:
            print("❌ 所有資料來源都失敗了")
            return False
        
        return True


def main():
    """主函數"""
    downloader = ExtendedDataDownloader()
    success = downloader.run()
    
    if success:
        print("\n🎉 資料下載流程完成！")
    else:
        print("\n💥 資料下載失敗，請檢查網路連接和套件安裝")
        print("\n📋 安裝建議:")
        print("pip install akshare qlib pandas numpy")


if __name__ == "__main__":
    main()
