#!/bin/bash
# 使用curl獲取股票資料的腳本
# Curl-based stock data crawler script

echo "================================================================================"
echo "🕷️ 使用 CURL 獲取真實股票資料"
echo "================================================================================"
echo "📅 目標期間: 2024-08-30 ~ 2025-07-31"
echo "📊 資料來源: 東方財富API"
echo "📁 輸出目錄: data/curl_crawled"
echo "--------------------------------------------------------------------------------"

# 創建輸出目錄
mkdir -p data/curl_crawled

# 定義股票清單 (CSI300重要成分股)
declare -a stocks=(
    "1.600000:浦發銀行"
    "1.600036:招商銀行" 
    "0.000001:平安銀行"
    "1.600016:民生銀行"
    "1.600519:貴州茅台"
    "0.000858:五糧液"
    "0.000596:古井貢酒"
    "0.000002:萬科A"
    "0.000063:中興通訊"
    "0.002415:海康威視"
    "1.601318:中國平安"
    "1.601628:中國人壽"
    "1.600887:伊利股份"
    "0.000895:雙匯發展"
    "1.600999:招商證券"
    "0.000100:TCL科技"
)

# 初始化CSV檔案
csv_file="data/curl_crawled/real_stock_data.csv"
echo "date,symbol,name,open,high,low,close,volume,amount" > "$csv_file"

# 成功和失敗計數
success_count=0
total_count=${#stocks[@]}
failed_stocks=()

echo "開始獲取 $total_count 隻股票的資料..."
echo ""

# 遍歷每隻股票
for i in "${!stocks[@]}"; do
    stock_info="${stocks[$i]}"
    secid="${stock_info%:*}"
    stock_name="${stock_info#*:}"
    
    # 從secid提取股票代碼
    if [[ $secid == 1.* ]]; then
        stock_code="${secid#1.}"
        market="SH"
    else
        stock_code="${secid#0.}"
        market="SZ"
    fi
    
    symbol="${stock_code}.${market}"
    
    echo "進度: $((i+1))/$total_count - 正在獲取 $symbol ($stock_name)..."
    
    # 構建API URL
    api_url="http://push2his.eastmoney.com/api/qt/stock/kline/get"
    params="secid=${secid}&ut=fa5fd1943c7b386f172d6893dbfba10b&fields1=f1,f2,f3,f4,f5,f6&fields2=f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61&klt=101&fqt=1&beg=20240830&end=20250731&lmt=1000"
    
    # 發送請求
    response=$(curl -s --connect-timeout 15 --max-time 30 "${api_url}?${params}")
    
    # 檢查響應
    if [[ -n "$response" && "$response" != "null" ]]; then
        # 使用Python解析JSON並轉換為CSV
        python3 -c "
import json
import sys

try:
    data = json.loads('$response')
    if 'data' in data and data['data'] and 'klines' in data['data']:
        klines = data['data']['klines']
        record_count = 0
        for kline in klines:
            parts = kline.split(',')
            if len(parts) >= 6:
                date = parts[0]
                open_price = parts[1]
                close_price = parts[2]
                high_price = parts[3]
                low_price = parts[4]
                volume = parts[5]
                amount = parts[6] if len(parts) > 6 else str(float(parts[5]) * float(parts[2]))
                
                print(f'{date},{symbol},{stock_name},{open_price},{high_price},{low_price},{close_price},{volume},{amount}')
                record_count += 1
        
        if record_count > 0:
            print(f'# SUCCESS: {record_count} records', file=sys.stderr)
        else:
            print(f'# ERROR: No valid records', file=sys.stderr)
    else:
        print(f'# ERROR: Invalid data structure', file=sys.stderr)
except Exception as e:
    print(f'# ERROR: {e}', file=sys.stderr)
" 2>temp_status.txt >>temp_data.csv
        
        # 檢查處理結果
        if grep -q "SUCCESS" temp_status.txt; then
            record_count=$(grep "SUCCESS" temp_status.txt | cut -d' ' -f3)
            echo "✅ $symbol 成功獲取 $record_count 條記錄"
            
            # 將資料追加到主CSV檔案
            cat temp_data.csv >> "$csv_file"
            ((success_count++))
        else
            echo "❌ $symbol 資料解析失敗"
            failed_stocks+=("$symbol")
        fi
        
        # 清理臨時檔案
        rm -f temp_data.csv temp_status.txt
        
    else
        echo "❌ $symbol API請求失敗"
        failed_stocks+=("$symbol")
    fi
    
    # 避免請求過於頻繁
    if (( (i+1) % 3 == 0 )); then
        echo "⏸️ 已處理 $((i+1)) 隻股票，休息2秒..."
        sleep 2
    else
        sleep 0.5
    fi
    
done

echo ""
echo "================================================================================"
echo "📊 爬取結果統計"
echo "================================================================================"
echo "✅ 成功處理: $success_count/$total_count 隻股票"

# 計算總記錄數 (排除標題行)
if [[ -f "$csv_file" ]]; then
    total_records=$(($(wc -l < "$csv_file") - 1))
    echo "📈 總記錄數: $total_records"
    
    if [[ $total_records -gt 0 ]]; then
        # 獲取日期範圍
        first_date=$(tail -n +2 "$csv_file" | head -1 | cut -d',' -f1)
        last_date=$(tail -1 "$csv_file" | cut -d',' -f1)
        echo "📅 資料期間: $first_date ~ $last_date"
        
        # 計算平均記錄數
        if [[ $success_count -gt 0 ]]; then
            avg_records=$((total_records / success_count))
            echo "📊 平均每隻股票: $avg_records 條記錄"
        fi
        
        echo ""
        echo "✅ 資料已儲存到: $csv_file"
    fi
fi

# 處理失敗的股票
if [[ ${#failed_stocks[@]} -gt 0 ]]; then
    echo ""
    echo "❌ 獲取失敗的股票:"
    for failed_stock in "${failed_stocks[@]}"; do
        echo "   $failed_stock"
    done
    
    # 儲存失敗清單
    failed_file="data/curl_crawled/failed_stocks.txt"
    printf '%s\n' "${failed_stocks[@]}" > "$failed_file"
    echo "📝 失敗清單已儲存到: $failed_file"
fi

echo ""
if [[ $success_count -gt 0 ]]; then
    echo "🎉 真實資料爬取完成！"
    echo "📋 下一步:"
    echo "1. 驗證資料品質: python scripts/validate_extended_data.py"
    echo "2. 執行回測: python finetune/standalone_backtest.py"
else
    echo "❌ 爬取失敗，沒有獲取到任何真實資料"
    echo "🔧 建議:"
    echo "1. 檢查網路連接"
    echo "2. 確認API服務可用性"
    echo "3. 稍後重試"
fi

echo "================================================================================"
