#!/usr/bin/env python3
"""
擴展資料驗證腳本 - 驗證2024-07到2025-07資料品質
Extended Data Validation Script - Validate 2024-07 to 2025-07 data quality
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加專案根目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

class ExtendedDataValidator:
    """擴展資料驗證器"""
    
    def __init__(self):
        self.target_start = datetime(2024, 7, 1)
        self.target_end = datetime(2025, 7, 31)
        self.data_dir = "data/extended"
        
    def validate_file(self, file_path: str) -> dict:
        """驗證單個資料檔案"""
        if not os.path.exists(file_path):
            return {"status": "ERROR", "message": f"File not found: {file_path}"}
        
        try:
            print(f"\n📁 驗證檔案: {file_path}")
            
            # 讀取資料
            df = pd.read_csv(file_path, encoding='utf-8')
            
            # 基本資訊
            print(f"📊 資料形狀: {df.shape}")
            print(f"📋 欄位: {list(df.columns)}")
            
            # 檢查必要欄位
            required_fields = ['open', 'high', 'low', 'close', 'volume']
            missing_fields = [field for field in required_fields if field not in df.columns]
            
            if missing_fields:
                return {
                    "status": "ERROR", 
                    "message": f"Missing required fields: {missing_fields}"
                }
            
            # 處理日期欄位
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
                date_col = 'date'
            elif 'timestamps' in df.columns:
                df['timestamps'] = pd.to_datetime(df['timestamps'])
                date_col = 'timestamps'
            else:
                return {"status": "ERROR", "message": "No date column found"}
            
            # 日期範圍檢查
            min_date = df[date_col].min()
            max_date = df[date_col].max()
            
            print(f"📅 日期範圍: {min_date.date()} 到 {max_date.date()}")
            
            # 檢查是否涵蓋目標期間
            covers_start = min_date <= self.target_start
            covers_end = max_date >= self.target_end
            
            coverage_status = "✅" if (covers_start and covers_end) else "⚠️"
            print(f"{coverage_status} 目標期間覆蓋: 開始 {covers_start}, 結束 {covers_end}")
            
            # 資料品質檢查
            quality_results = self._check_data_quality(df)
            
            # 統計資訊
            if 'symbol' in df.columns:
                unique_symbols = df['symbol'].nunique()
                print(f"📈 股票數量: {unique_symbols}")
                
                # 每隻股票的資料量
                symbol_counts = df['symbol'].value_counts()
                print(f"📊 每隻股票平均資料點: {symbol_counts.mean():.0f}")
                print(f"📊 資料點範圍: {symbol_counts.min()} - {symbol_counts.max()}")
            
            # 計算交易日數量
            trading_days = df[date_col].dt.date.nunique()
            expected_days = self._calculate_expected_trading_days()
            
            print(f"📅 實際交易日: {trading_days}")
            print(f"📅 預期交易日: {expected_days}")
            print(f"📅 覆蓋率: {trading_days/expected_days*100:.1f}%")
            
            return {
                "status": "SUCCESS",
                "file_path": file_path,
                "shape": df.shape,
                "date_range": (min_date, max_date),
                "covers_target": covers_start and covers_end,
                "trading_days": trading_days,
                "expected_days": expected_days,
                "coverage_rate": trading_days/expected_days,
                "quality": quality_results,
                "symbols": df['symbol'].nunique() if 'symbol' in df.columns else 1
            }
            
        except Exception as e:
            return {"status": "ERROR", "message": f"Validation failed: {str(e)}"}
    
    def _check_data_quality(self, df: pd.DataFrame) -> dict:
        """檢查資料品質"""
        quality = {}
        
        # 檢查缺失值
        missing_data = df.isnull().sum()
        quality['missing_values'] = missing_data.to_dict()
        quality['has_missing'] = missing_data.sum() > 0
        
        # 檢查價格邏輯
        if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            # High應該 >= max(open, close)
            high_errors = (df['high'] < df[['open', 'close']].max(axis=1)).sum()
            
            # Low應該 <= min(open, close)  
            low_errors = (df['low'] > df[['open', 'close']].min(axis=1)).sum()
            
            # High應該 >= Low
            high_low_errors = (df['high'] < df['low']).sum()
            
            quality['price_logic_errors'] = {
                'high_errors': high_errors,
                'low_errors': low_errors, 
                'high_low_errors': high_low_errors,
                'total_errors': high_errors + low_errors + high_low_errors
            }
        
        # 檢查負值
        numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'amount']
        negative_values = {}
        for col in numeric_cols:
            if col in df.columns:
                negative_count = (df[col] < 0).sum()
                negative_values[col] = negative_count
        
        quality['negative_values'] = negative_values
        
        # 檢查異常值 (使用IQR方法)
        outliers = {}
        for col in ['open', 'high', 'low', 'close']:
            if col in df.columns:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outlier_count = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
                outliers[col] = {
                    'count': outlier_count,
                    'percentage': outlier_count / len(df) * 100
                }
        
        quality['outliers'] = outliers
        
        return quality
    
    def _calculate_expected_trading_days(self) -> int:
        """計算預期的交易日數量 (排除週末)"""
        current_date = self.target_start
        trading_days = 0
        
        while current_date <= self.target_end:
            # 排除週末 (週六=5, 週日=6)
            if current_date.weekday() < 5:
                trading_days += 1
            current_date += timedelta(days=1)
        
        return trading_days
    
    def generate_report(self) -> str:
        """生成驗證報告"""
        print("="*80)
        print("📋 擴展資料驗證報告 EXTENDED DATA VALIDATION REPORT")
        print("="*80)
        
        # 檢查所有可能的資料檔案
        data_files = [
            "data/extended/csi300_extended_data.csv",
            "data/extended/csi300_qlib_data.csv",
            "data/extended/synthetic_extended_data.csv",
            "examples/data/XSHG_5min_600977.csv"
        ]
        
        validation_results = []
        
        for file_path in data_files:
            if os.path.exists(file_path):
                result = self.validate_file(file_path)
                validation_results.append(result)
            else:
                print(f"\n❌ 檔案不存在: {file_path}")
        
        if not validation_results:
            print("\n❌ 沒有找到任何資料檔案！")
            print("\n📋 建議:")
            print("1. 執行資料下載腳本: python scripts/download_extended_data.py")
            print("2. 檢查資料檔案路徑")
            return "No data files found"
        
        # 總結報告
        print(f"\n{'='*80}")
        print("📊 驗證總結 VALIDATION SUMMARY")
        print("="*80)
        
        successful_files = [r for r in validation_results if r['status'] == 'SUCCESS']
        
        if successful_files:
            print(f"✅ 成功驗證 {len(successful_files)} 個檔案")
            
            # 找到最佳檔案 (覆蓋率最高)
            best_file = max(successful_files, key=lambda x: x.get('coverage_rate', 0))
            
            print(f"\n🏆 推薦使用檔案: {best_file['file_path']}")
            print(f"📅 覆蓋率: {best_file['coverage_rate']*100:.1f}%")
            print(f"📊 交易日: {best_file['trading_days']}/{best_file['expected_days']}")
            print(f"📈 股票數: {best_file['symbols']}")
            
            # 品質評估
            quality = best_file['quality']
            if quality['has_missing']:
                print("⚠️  存在缺失值")
            else:
                print("✅ 無缺失值")
            
            total_price_errors = quality.get('price_logic_errors', {}).get('total_errors', 0)
            if total_price_errors > 0:
                print(f"⚠️  {total_price_errors} 個價格邏輯錯誤")
            else:
                print("✅ 價格邏輯正確")
        
        else:
            print("❌ 沒有成功驗證的檔案")
        
        # 建議
        print(f"\n📋 建議 RECOMMENDATIONS")
        print("-"*40)
        
        if not successful_files:
            print("1. 檢查資料下載是否成功")
            print("2. 執行: python scripts/download_extended_data.py")
            print("3. 確認網路連接和API可用性")
        else:
            best_coverage = max(r.get('coverage_rate', 0) for r in successful_files)
            if best_coverage < 0.8:
                print("1. 資料覆蓋率不足，建議獲取更完整的資料")
                print("2. 檢查資料來源的可用性")
            
            if any(r['quality']['has_missing'] for r in successful_files):
                print("3. 處理缺失值問題")
            
            print("4. 準備執行回測: python finetune/standalone_backtest.py")
        
        return "Validation completed"
    
    def run(self):
        """執行驗證流程"""
        return self.generate_report()


def main():
    """主函數"""
    validator = ExtendedDataValidator()
    validator.run()


if __name__ == "__main__":
    main()
