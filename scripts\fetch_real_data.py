#!/usr/bin/env python3
"""
獲取真實股票資料腳本 - 使用urllib獲取東方財富API資料
Fetch Real Stock Data Script - Using urllib to get Eastmoney API data
"""

import urllib.request
import urllib.parse
import json
import csv
import os
import time
from datetime import datetime

def fetch_stock_data(secid, stock_code, market, stock_name):
    """獲取單隻股票的資料"""
    try:
        print(f"📈 正在獲取 {stock_code}.{market} ({stock_name})...")
        
        # 構建API URL
        base_url = "http://push2his.eastmoney.com/api/qt/stock/kline/get"
        params = {
            'secid': secid,
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fields1': 'f1,f2,f3,f4,f5,f6',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
            'klt': '101',  # 日K線
            'fqt': '1',    # 前復權
            'beg': '20240830',  # 開始日期
            'end': '20250731',  # 結束日期
            'lmt': '1000'
        }
        
        url = base_url + "?" + urllib.parse.urlencode(params)
        
        # 設置請求頭
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
        
        # 創建請求
        req = urllib.request.Request(url)
        for key, value in headers.items():
            req.add_header(key, value)
        
        # 發送請求
        with urllib.request.urlopen(req, timeout=15) as response:
            if response.status == 200:
                data = json.loads(response.read().decode('utf-8'))
                
                if 'data' in data and data['data'] and 'klines' in data['data']:
                    klines = data['data']['klines']
                    
                    stock_data = []
                    for kline in klines:
                        try:
                            parts = kline.split(',')
                            if len(parts) >= 6:
                                stock_data.append({
                                    'date': parts[0],
                                    'symbol': f"{stock_code}.{market}",
                                    'name': stock_name,
                                    'open': float(parts[1]),
                                    'close': float(parts[2]),
                                    'high': float(parts[3]),
                                    'low': float(parts[4]),
                                    'volume': float(parts[5]),
                                    'amount': float(parts[6]) if len(parts) > 6 else float(parts[5]) * float(parts[2])
                                })
                        except (ValueError, IndexError):
                            continue
                    
                    if stock_data:
                        print(f"✅ {stock_code}.{market} 成功獲取 {len(stock_data)} 條記錄")
                        return stock_data
                    else:
                        print(f"⚠️ {stock_code}.{market} 沒有有效資料")
                else:
                    print(f"⚠️ {stock_code}.{market} API返回空資料")
            else:
                print(f"❌ {stock_code}.{market} HTTP錯誤: {response.status}")
    
    except Exception as e:
        print(f"❌ {stock_code}.{market} 獲取失敗: {e}")
    
    return []

def main():
    """主函數"""
    print("="*80)
    print("🕷️ 獲取真實股票資料")
    print("="*80)
    print("📅 目標期間: 2024-08-30 ~ 2025-07-31")
    print("📊 資料來源: 東方財富API")
    print("-"*80)
    
    # 創建輸出目錄
    output_dir = "data/real_crawled"
    os.makedirs(output_dir, exist_ok=True)
    
    # 股票清單 (CSI300重要成分股)
    stocks = [
        ('1.600000', '600000', 'SH', '浦發銀行'),
        ('1.600036', '600036', 'SH', '招商銀行'),
        ('0.000001', '000001', 'SZ', '平安銀行'),
        ('1.600016', '600016', 'SH', '民生銀行'),
        ('1.600519', '600519', 'SH', '貴州茅台'),
        ('0.000858', '000858', 'SZ', '五糧液'),
        ('0.000596', '000596', 'SZ', '古井貢酒'),
        ('0.000002', '000002', 'SZ', '萬科A'),
        ('0.000063', '000063', 'SZ', '中興通訊'),
        ('0.002415', '002415', 'SZ', '海康威視'),
        ('1.601318', '601318', 'SH', '中國平安'),
        ('1.601628', '601628', 'SH', '中國人壽'),
        ('1.600887', '600887', 'SH', '伊利股份'),
        ('0.000895', '000895', 'SZ', '雙匯發展'),
        ('1.600999', '600999', 'SH', '招商證券'),
        ('0.000100', '000100', 'SZ', 'TCL科技')
    ]
    
    all_data = []
    success_count = 0
    failed_stocks = []
    
    print(f"開始獲取 {len(stocks)} 隻股票的資料...")
    print("")
    
    for i, (secid, stock_code, market, stock_name) in enumerate(stocks, 1):
        print(f"進度: {i}/{len(stocks)}")
        
        try:
            stock_data = fetch_stock_data(secid, stock_code, market, stock_name)
            
            if stock_data:
                all_data.extend(stock_data)
                success_count += 1
            else:
                failed_stocks.append(f"{stock_code}.{market}")
            
            # 避免請求過於頻繁
            if i % 3 == 0:
                print(f"⏸️ 已處理 {i} 隻股票，休息2秒...")
                time.sleep(2)
            else:
                time.sleep(0.5)
                
        except Exception as e:
            print(f"❌ 處理 {stock_code}.{market} 時發生錯誤: {e}")
            failed_stocks.append(f"{stock_code}.{market}")
            continue
    
    # 處理結果
    if all_data:
        # 按日期和股票代碼排序
        all_data.sort(key=lambda x: (x['symbol'], x['date']))
        
        # 儲存到CSV
        csv_file = os.path.join(output_dir, "real_stock_data.csv")
        
        try:
            with open(csv_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['date', 'symbol', 'name', 'open', 'high', 'low', 'close', 'volume', 'amount']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for row in all_data:
                    writer.writerow(row)
            
            print(f"\n{'='*80}")
            print("📊 爬取結果統計")
            print("="*80)
            print(f"✅ 成功處理: {success_count}/{len(stocks)} 隻股票")
            print(f"📈 總記錄數: {len(all_data)}")
            
            # 計算日期範圍
            dates = [item['date'] for item in all_data]
            if dates:
                print(f"📅 資料期間: {min(dates)} ~ {max(dates)}")
            
            # 計算每隻股票的資料量
            symbol_counts = {}
            for item in all_data:
                symbol = item['symbol']
                symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
            
            if symbol_counts:
                avg_records = sum(symbol_counts.values()) / len(symbol_counts)
                print(f"📊 平均每隻股票: {avg_records:.0f} 條記錄")
                
                print(f"\n✅ 成功獲取資料的股票:")
                for symbol, count in symbol_counts.items():
                    print(f"   {symbol}: {count} 條記錄")
            
            print(f"\n✅ 資料已儲存到: {csv_file}")
            
            # 處理失敗的股票
            if failed_stocks:
                print(f"\n❌ 獲取失敗的股票:")
                for symbol in failed_stocks:
                    print(f"   {symbol}")
                
                # 儲存失敗清單
                failed_file = os.path.join(output_dir, "failed_stocks.txt")
                with open(failed_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(failed_stocks))
                print(f"📝 失敗清單已儲存到: {failed_file}")
            
            print(f"\n🎉 真實資料爬取完成！")
            print(f"📋 下一步:")
            print(f"1. 驗證資料品質: python scripts/validate_extended_data.py")
            print(f"2. 執行回測: python finetune/standalone_backtest.py")
            
            return True
            
        except Exception as e:
            print(f"❌ 儲存資料失敗: {e}")
            return False
    
    else:
        print(f"\n❌ 爬取失敗，沒有獲取到任何真實資料")
        print(f"🔧 建議:")
        print(f"1. 檢查網路連接")
        print(f"2. 確認API服務可用性")
        print(f"3. 稍後重試")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print(f"\n✅ 真實資料獲取任務成功完成！")
        else:
            print(f"\n❌ 真實資料獲取任務失敗！")
    except KeyboardInterrupt:
        print(f"\n⏹️ 用戶中斷獲取任務")
    except Exception as e:
        print(f"\n💥 獲取過程中發生錯誤: {e}")
