#!/usr/bin/env python3
"""
爬蟲測試腳本 - 快速測試爬蟲功能
Crawler Test Script - Quick test for crawler functionality
"""

import os
import sys
import time
import json
from datetime import datetime

def test_api_connectivity():
    """測試API連通性"""
    print("🔍 測試API連通性...")
    
    try:
        import requests
        
        # 測試東方財富API
        print("📡 測試東方財富API...")
        url = "http://push2his.eastmoney.com/api/qt/stock/kline/get"
        params = {
            'secid': '1.600000',  # 浦發銀行
            'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
            'fields1': 'f1,f2,f3,f4,f5,f6',
            'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
            'klt': '101',
            'fqt': '1',
            'beg': '20240830',
            'end': '20240930',
            'lmt': '10'
        }
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and data['data']:
                print("✅ 東方財富API連通正常")
                if 'klines' in data['data']:
                    print(f"📊 測試獲取到 {len(data['data']['klines'])} 條記錄")
                return True
            else:
                print("⚠️ 東方財富API返回空資料")
        else:
            print(f"❌ 東方財富API請求失敗: {response.status_code}")
        
    except ImportError:
        print("❌ requests 模組未安裝，請執行: pip install requests")
    except Exception as e:
        print(f"❌ API測試失敗: {e}")
    
    return False

def test_data_generation():
    """測試模擬資料生成"""
    print("\n🔄 測試模擬資料生成...")
    
    try:
        from datetime import datetime, timedelta
        import random
        
        # 生成測試資料
        start_date = datetime(2024, 8, 30)
        end_date = datetime(2024, 9, 30)
        
        test_data = []
        current_date = start_date
        current_price = 10.0
        
        random.seed(42)  # 確保可重現
        
        while current_date <= end_date:
            if current_date.weekday() < 5:  # 工作日
                # 模擬價格變動
                daily_return = random.gauss(0.001, 0.02)
                current_price *= (1 + daily_return)
                
                # 生成OHLC
                high = current_price * (1 + abs(random.gauss(0, 0.01)))
                low = current_price * (1 - abs(random.gauss(0, 0.01)))
                open_price = low + (high - low) * random.random()
                close_price = current_price
                
                # 確保價格邏輯
                high = max(high, open_price, close_price)
                low = min(low, open_price, close_price)
                
                volume = random.lognormal(12, 1)
                amount = close_price * volume
                
                test_data.append({
                    'date': current_date.strftime('%Y-%m-%d'),
                    'symbol': '600000.SH',
                    'open': round(open_price, 2),
                    'high': round(high, 2),
                    'low': round(low, 2),
                    'close': round(close_price, 2),
                    'volume': int(volume),
                    'amount': round(amount, 2)
                })
                
                current_price = close_price
            
            current_date += timedelta(days=1)
        
        print(f"✅ 成功生成 {len(test_data)} 條測試資料")
        print(f"📅 日期範圍: {test_data[0]['date']} ~ {test_data[-1]['date']}")
        
        # 儲存測試資料
        output_dir = "data/test"
        os.makedirs(output_dir, exist_ok=True)
        
        import csv
        output_file = os.path.join(output_dir, "test_data.csv")
        
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['date', 'symbol', 'open', 'high', 'low', 'close', 'volume', 'amount']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for row in test_data:
                writer.writerow(row)
        
        print(f"💾 測試資料已儲存到: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ 模擬資料生成失敗: {e}")
        return False

def test_file_operations():
    """測試檔案操作"""
    print("\n📁 測試檔案操作...")
    
    try:
        # 檢查目錄創建
        test_dirs = ["data/test", "data/crawled", "data/scraped"]
        
        for dir_path in test_dirs:
            os.makedirs(dir_path, exist_ok=True)
            if os.path.exists(dir_path):
                print(f"✅ 目錄創建成功: {dir_path}")
            else:
                print(f"❌ 目錄創建失敗: {dir_path}")
                return False
        
        # 測試檔案寫入
        test_file = "data/test/test_write.txt"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("測試檔案寫入\nTest file write\n")
        
        if os.path.exists(test_file):
            print(f"✅ 檔案寫入成功: {test_file}")
            
            # 測試檔案讀取
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if "測試檔案寫入" in content:
                    print("✅ 檔案讀取成功")
                    return True
        
        return False
        
    except Exception as e:
        print(f"❌ 檔案操作測試失敗: {e}")
        return False

def run_crawler_test():
    """執行爬蟲測試"""
    print("="*60)
    print("🧪 爬蟲功能測試")
    print("="*60)
    
    # 測試結果
    results = {
        'api_connectivity': False,
        'data_generation': False,
        'file_operations': False
    }
    
    # 1. 測試API連通性
    results['api_connectivity'] = test_api_connectivity()
    
    # 2. 測試模擬資料生成
    results['data_generation'] = test_data_generation()
    
    # 3. 測試檔案操作
    results['file_operations'] = test_file_operations()
    
    # 總結
    print(f"\n{'='*60}")
    print("📊 測試結果總結")
    print("="*60)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print(f"\n🎉 所有測試通過！爬蟲功能正常")
        print(f"📋 下一步:")
        print(f"1. 執行簡化爬蟲: python scripts/simple_data_crawler.py")
        print(f"2. 執行完整爬蟲: python scripts/web_scraper_extended_data.py")
    else:
        print(f"\n⚠️ 部分測試失敗，請檢查:")
        
        if not results['api_connectivity']:
            print(f"- 網路連接和API可用性")
            print(f"- 防火牆設定")
        
        if not results['data_generation']:
            print(f"- Python環境和模組安裝")
            print(f"- 隨機數生成功能")
        
        if not results['file_operations']:
            print(f"- 檔案系統權限")
            print(f"- 磁碟空間")
    
    return all_passed

def main():
    """主函數"""
    try:
        success = run_crawler_test()
        
        if success:
            print(f"\n✅ 測試完成，爬蟲功能正常！")
        else:
            print(f"\n❌ 測試發現問題，請修復後重試")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 用戶中斷測試")
    except Exception as e:
        print(f"\n💥 測試過程中發生錯誤: {e}")

if __name__ == "__main__":
    main()
