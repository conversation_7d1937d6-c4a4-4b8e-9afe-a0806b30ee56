#!/usr/bin/env python3
"""
網路爬蟲腳本 - 抓取缺失的股票資料 (2024-08-30 ~ 2025-07-31)
Web Scraper Script - Fetch missing stock data (2024-08-30 ~ 2025-07-31)
"""

import os
import sys
import time
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import json
import warnings
warnings.filterwarnings('ignore')

# 添加專案根目錄到路徑
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

class StockDataScraper:
    """股票資料爬蟲類"""
    
    def __init__(self):
        self.start_date = "2024-08-30"
        self.end_date = "2025-07-31"
        self.output_dir = "data/scraped"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://finance.sina.com.cn/'
        }
        
        # 確保輸出目錄存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # CSI 300 成分股代碼 (部分示例)
        self.csi300_symbols = [
            '000001.SZ', '000002.SZ', '000858.SZ', '000895.SZ', '000938.SZ',
            '600000.SH', '600036.SH', '600519.SH', '600887.SH', '600999.SH',
            '000001.SZ', '000002.SZ', '000063.SZ', '000069.SZ', '000100.SZ',
            '600004.SH', '600009.SH', '600010.SH', '600011.SH', '600015.SH'
        ]
    
    def get_csi300_list(self) -> List[str]:
        """獲取CSI300成分股清單"""
        try:
            # 嘗試從東方財富獲取CSI300成分股
            url = "http://push2.eastmoney.com/api/qt/clist/get"
            params = {
                'pn': '1',
                'pz': '300',
                'po': '1',
                'np': '1',
                'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                'fltt': '2',
                'invt': '2',
                'fid': 'f3',
                'fs': 'b:BK0537',  # CSI300板塊
                'fields': 'f12,f14'
            }
            
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and 'diff' in data['data']:
                    symbols = []
                    for item in data['data']['diff']:
                        symbol = item['f12']
                        # 轉換為標準格式
                        if symbol.startswith('6'):
                            symbols.append(f"{symbol}.SH")
                        else:
                            symbols.append(f"{symbol}.SZ")
                    
                    print(f"✅ 成功獲取 {len(symbols)} 隻CSI300成分股")
                    return symbols
            
        except Exception as e:
            print(f"⚠️ 獲取CSI300清單失敗: {e}")
        
        # 使用預設清單
        print(f"🔄 使用預設股票清單 ({len(self.csi300_symbols)} 隻)")
        return self.csi300_symbols
    
    def scrape_sina_finance(self, symbol: str) -> Optional[pd.DataFrame]:
        """從新浪財經抓取股票資料"""
        try:
            # 轉換股票代碼格式
            if symbol.endswith('.SH'):
                sina_symbol = 'sh' + symbol.replace('.SH', '')
            elif symbol.endswith('.SZ'):
                sina_symbol = 'sz' + symbol.replace('.SZ', '')
            else:
                return None
            
            # 新浪財經API
            url = f"https://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData"
            params = {
                'symbol': sina_symbol,
                'scale': '240',  # 日線
                'ma': 'no',
                'datalen': '500'  # 獲取最近500個交易日
            }
            
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                # 解析JSON資料
                data_text = response.text
                if data_text and data_text != 'null':
                    data = json.loads(data_text)
                    
                    if data and isinstance(data, list):
                        df_data = []
                        for item in data:
                            try:
                                date = datetime.strptime(item['day'], '%Y-%m-%d')
                                # 只保留目標期間的資料
                                if datetime.strptime(self.start_date, '%Y-%m-%d') <= date <= datetime.strptime(self.end_date, '%Y-%m-%d'):
                                    df_data.append({
                                        'date': item['day'],
                                        'symbol': symbol,
                                        'open': float(item['open']),
                                        'high': float(item['high']),
                                        'low': float(item['low']),
                                        'close': float(item['close']),
                                        'volume': float(item['volume']),
                                        'amount': float(item['volume']) * float(item['close'])  # 估算成交額
                                    })
                            except (KeyError, ValueError, TypeError):
                                continue
                        
                        if df_data:
                            df = pd.DataFrame(df_data)
                            df['date'] = pd.to_datetime(df['date'])
                            df = df.sort_values('date')
                            return df
            
        except Exception as e:
            print(f"❌ 抓取 {symbol} 失敗 (新浪): {e}")
        
        return None
    
    def scrape_eastmoney(self, symbol: str) -> Optional[pd.DataFrame]:
        """從東方財富抓取股票資料"""
        try:
            # 轉換股票代碼格式
            if symbol.endswith('.SH'):
                em_symbol = '1.' + symbol.replace('.SH', '')
            elif symbol.endswith('.SZ'):
                em_symbol = '0.' + symbol.replace('.SZ', '')
            else:
                return None
            
            # 東方財富API
            url = "http://push2his.eastmoney.com/api/qt/stock/kline/get"
            params = {
                'secid': em_symbol,
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'fields1': 'f1,f2,f3,f4,f5,f6',
                'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
                'klt': '101',  # 日K線
                'fqt': '1',    # 前復權
                'beg': self.start_date.replace('-', ''),
                'end': self.end_date.replace('-', ''),
                'lmt': '1000'
            }
            
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and data['data'] and 'klines' in data['data']:
                    klines = data['data']['klines']
                    
                    df_data = []
                    for kline in klines:
                        try:
                            parts = kline.split(',')
                            if len(parts) >= 6:
                                df_data.append({
                                    'date': parts[0],
                                    'symbol': symbol,
                                    'open': float(parts[1]),
                                    'close': float(parts[2]),
                                    'high': float(parts[3]),
                                    'low': float(parts[4]),
                                    'volume': float(parts[5]),
                                    'amount': float(parts[6]) if len(parts) > 6 else float(parts[5]) * float(parts[2])
                                })
                        except (ValueError, IndexError):
                            continue
                    
                    if df_data:
                        df = pd.DataFrame(df_data)
                        df['date'] = pd.to_datetime(df['date'])
                        df = df.sort_values('date')
                        return df
            
        except Exception as e:
            print(f"❌ 抓取 {symbol} 失敗 (東方財富): {e}")
        
        return None
    
    def scrape_163_finance(self, symbol: str) -> Optional[pd.DataFrame]:
        """從網易財經抓取股票資料"""
        try:
            # 轉換股票代碼格式
            if symbol.endswith('.SH'):
                netease_symbol = '0' + symbol.replace('.SH', '')
            elif symbol.endswith('.SZ'):
                netease_symbol = '1' + symbol.replace('.SZ', '')
            else:
                return None
            
            # 計算日期範圍
            start_date_obj = datetime.strptime(self.start_date, '%Y-%m-%d')
            end_date_obj = datetime.strptime(self.end_date, '%Y-%m-%d')
            
            # 網易財經API
            url = f"http://img1.money.126.net/data/hs/kline/day/history/{start_date_obj.year}/{netease_symbol}.json"
            
            response = requests.get(url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and isinstance(data['data'], list):
                    df_data = []
                    for item in data['data']:
                        try:
                            if len(item) >= 6:
                                date = datetime.strptime(item[0], '%Y-%m-%d')
                                # 檢查日期範圍
                                if start_date_obj <= date <= end_date_obj:
                                    df_data.append({
                                        'date': item[0],
                                        'symbol': symbol,
                                        'open': float(item[1]),
                                        'high': float(item[2]),
                                        'low': float(item[3]),
                                        'close': float(item[4]),
                                        'volume': float(item[5]),
                                        'amount': float(item[5]) * float(item[4])  # 估算成交額
                                    })
                        except (ValueError, IndexError, TypeError):
                            continue
                    
                    if df_data:
                        df = pd.DataFrame(df_data)
                        df['date'] = pd.to_datetime(df['date'])
                        df = df.sort_values('date')
                        return df
            
        except Exception as e:
            print(f"❌ 抓取 {symbol} 失敗 (網易): {e}")
        
        return None
    
    def scrape_stock_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """嘗試多個資料源抓取股票資料"""
        print(f"📈 正在抓取 {symbol} 資料...")
        
        # 嘗試不同的資料源
        scrapers = [
            self.scrape_eastmoney,
            self.scrape_sina_finance,
            self.scrape_163_finance
        ]
        
        for scraper in scrapers:
            try:
                df = scraper(symbol)
                if df is not None and not df.empty:
                    print(f"✅ {symbol} 成功抓取 {len(df)} 條記錄")
                    return df
                
                # 添加延遲避免被封IP
                time.sleep(1)
                
            except Exception as e:
                print(f"⚠️ {symbol} 抓取失敗: {e}")
                continue
        
        print(f"❌ {symbol} 所有資料源都失敗")
        return None
    
    def validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """驗證和清理資料"""
        if df.empty:
            return df
        
        # 移除重複記錄
        df = df.drop_duplicates(subset=['date', 'symbol'])
        
        # 檢查價格邏輯
        df = df[
            (df['high'] >= df[['open', 'close']].max(axis=1)) &
            (df['low'] <= df[['open', 'close']].min(axis=1)) &
            (df['high'] >= df['low']) &
            (df['volume'] >= 0) &
            (df['amount'] >= 0)
        ]
        
        # 移除異常值 (價格變動超過20%的記錄)
        df = df.sort_values(['symbol', 'date'])
        df['price_change'] = df.groupby('symbol')['close'].pct_change()
        df = df[abs(df['price_change']) <= 0.2]  # 移除單日漲跌幅超過20%的異常資料
        df = df.drop('price_change', axis=1)
        
        return df
    
    def run_scraping(self):
        """執行完整的爬蟲流程"""
        print("="*80)
        print("🕷️ 開始爬取缺失的股票資料")
        print("="*80)
        print(f"📅 目標期間: {self.start_date} ~ {self.end_date}")
        print(f"📁 輸出目錄: {self.output_dir}")
        print("-"*80)
        
        # 獲取股票清單
        print("\n1. 獲取CSI300成分股清單...")
        symbols = self.get_csi300_list()
        
        # 開始爬取資料
        print(f"\n2. 開始爬取 {len(symbols)} 隻股票的資料...")
        all_data = []
        success_count = 0
        failed_symbols = []
        
        for i, symbol in enumerate(symbols, 1):
            print(f"\n進度: {i}/{len(symbols)} - {symbol}")
            
            try:
                df = self.scrape_stock_data(symbol)
                
                if df is not None and not df.empty:
                    # 驗證資料
                    df = self.validate_data(df)
                    
                    if not df.empty:
                        all_data.append(df)
                        success_count += 1
                    else:
                        failed_symbols.append(symbol)
                else:
                    failed_symbols.append(symbol)
                
                # 每10隻股票休息一下
                if i % 10 == 0:
                    print(f"⏸️ 已處理 {i} 隻股票，休息2秒...")
                    time.sleep(2)
                
            except Exception as e:
                print(f"❌ 處理 {symbol} 時發生錯誤: {e}")
                failed_symbols.append(symbol)
                continue
        
        # 合併所有資料
        if all_data:
            print(f"\n3. 合併資料...")
            combined_df = pd.concat(all_data, ignore_index=True)
            combined_df = combined_df.sort_values(['symbol', 'date'])
            
            # 儲存資料
            output_file = os.path.join(self.output_dir, "scraped_stock_data.csv")
            combined_df.to_csv(output_file, index=False, encoding='utf-8')
            
            print(f"\n✅ 爬取完成！")
            print(f"📊 成功: {success_count}/{len(symbols)} 隻股票")
            print(f"📁 資料已儲存到: {output_file}")
            print(f"📈 總記錄數: {len(combined_df)}")
            print(f"📅 日期範圍: {combined_df['date'].min().date()} ~ {combined_df['date'].max().date()}")
            
            # 儲存失敗清單
            if failed_symbols:
                failed_file = os.path.join(self.output_dir, "failed_symbols.txt")
                with open(failed_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(failed_symbols))
                print(f"⚠️ 失敗清單已儲存到: {failed_file}")
            
            return combined_df
        
        else:
            print("\n❌ 沒有成功抓取任何資料！")
            print("🔧 建議:")
            print("1. 檢查網路連接")
            print("2. 確認資料源API是否可用")
            print("3. 調整請求頻率和重試機制")
            return None


def main():
    """主函數"""
    scraper = StockDataScraper()
    
    try:
        result = scraper.run_scraping()
        
        if result is not None:
            print(f"\n🎉 爬蟲任務完成！")
            print(f"📋 下一步:")
            print(f"1. 檢查資料品質: python scripts/validate_extended_data.py")
            print(f"2. 執行回測: python finetune/standalone_backtest.py")
        else:
            print(f"\n💥 爬蟲任務失敗！")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 用戶中斷爬蟲任務")
    except Exception as e:
        print(f"\n💥 爬蟲過程中發生錯誤: {e}")


if __name__ == "__main__":
    main()
